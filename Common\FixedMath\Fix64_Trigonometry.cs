﻿using System;
using System.IO;

namespace FixedMath
{
    public partial struct Fix64
    {
        /// <summary>
        /// 64位定点数正弦
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入不在区间 [-2 pi, 2 pi] 时,
        /// 计算 rawValue / 2 pi 时会产生较大误差, 输入数据的绝对值越大，误差越大。
        /// 
        /// 如果输入的弧度很大的话，可以用 Fix64 表示，
        /// 如 1000 * Math.PI -> 1000 * Fix64.pi，这样在计算时就不会有太大的误差了
        /// 
        /// 当输入的定义域：[-2 pi, 2 pi]时，有较好的精度，误差 < 1 / 10000
        /// 
        /// 测试方法：随机生成 [-2 pi, 2 pi] 的 10000 个样本，取误差最大的 10 个输出
        /// sin(-0.0454403041293619 )=-0.045379638671875  , -0.045424668059552  , diff=00.00004502938767700
        /// sin(-0.0155786117075342 )=-0.015533447265625  , -0.0155779815781415 , diff=00.00004453431251651
        /// sin(-0.0906368217382088 )=-0.0904693603515625 , -0.0905127752809934 , diff=00.00004341492943090
        /// sin(-0.0239408650385588 )=-0.023895263671875  , -0.0239385780930397 , diff=00.00004331442116469
        /// sin(-0.189468263950497  )=-0.18829345703125   , -0.188336701249245  , diff=00.00004324421799468
        /// sin(6.27670322249744    )=-0.006439208984375  , -0.0064820392888306 , diff=00.00004283030445560
        /// sin(-0.146223741342415  )=-0.145660400390625  , -0.145703220502086  , diff=00.00004282011146126
        /// sin(-0.158094736393964  )=-0.157394409179688  , -0.157436990373515  , diff=00.00004258119382719
        /// sin(-0.0175145760519785 )=-0.0174713134765625 , -0.0175136806027289 , diff=00.00004236712616636
        /// sin(-0.172636147960323  )=-0.171737670898438  , -0.17177990548372   , diff=00.00004223458528207
        /// </remarks>
        public static Fix64 Sin(Fix64 angle)
        {
            bool flipHorizontal;
            bool flipVertical;
            long num = ClampSinValue(angle.rawValue, out flipHorizontal, out flipVertical);
            long range_max = pi.rawValue / 2;
            if (num >= range_max)
            {
                num = range_max;
            }
            long value = SinLut[flipHorizontal ? ((SinLut.Length - 1) - ((int)num)) : ((int)num)];

            return FromRawValue(flipVertical ? -value : value);
        }

        static long ClampSinValue(long radian, out bool flipHorizontal, out bool flipVertical)
        {
            long num = radian % (pi.rawValue * 2);
            if (radian < 0L)
            {
                num += pi.rawValue * 2;
            }
            // sin(r) = sin(pi-r) = -sin(r-pi)
            flipVertical = num >= pi.rawValue;
            long num2 = num;
            while (num2 >= pi.rawValue)
            {
                num2 -= pi.rawValue;
            }
            // revert the SinLut
            flipHorizontal = num2 >= pi.rawValue / 2;
            long num3 = num2;
            if (num3 >= pi.rawValue / 2)
            {
                num3 -= pi.rawValue / 2;
            }
            return num3;
        }

        /// <summary>
        /// 64位定点数余弦
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入不在区间 [-2 pi, 2 pi] 时,
        /// 计算 rawValue / 2 pi 时会产生较大误差, 输入数据的绝对值越大，误差越大。
        /// 
        /// 如果输入的弧度很大的话，可以用 Fix64 表示，
        /// 如 1000 * Math.PI -> 1000 * Fix64.pi，这样在计算时就不会有太大的误差了
        /// 
        /// 当输入的定义域：[-2 pi, 2 pi]时，有较好的精度，误差 < 1 / 10000
        /// 
        /// 测试方法：随机生成 [-2 pi, 2 pi] 的 10000 个样本，取误差最大的 10 个输出
        /// cos(1.58022982017644    )=-0.0093994140625    , -0.00943335346648246, diff=00.00003393940398246
        /// cos(-1.6279291331465    )=-0.05706787109375   , -0.0571017296770128 , diff=00.00003385858326282
        /// cos(-1.58906511099445   )=-0.0182342529296875 , -0.0182677680200311 , diff=00.00003351509034358
        /// cos(-1.67138595735218   )=-0.100387573242188  , -0.100420084119051  , diff=00.00003251087686305
        /// cos(1.67709238238748    )=-0.106063842773438  , -0.106095998090337  , diff=00.00003215531689912
        /// cos(1.74244641896158    )=-0.1707763671875    , -0.170808423707307  , diff=00.00003205651980656
        /// cos(1.81581083025509    )=-0.242538452148438  , -0.242570395111137  , diff=00.00003194296269923
        /// cos(1.72889552582046    )=-0.15740966796875   , -0.157441397350225  , diff=00.00003172938147511
        /// cos(-1.64275897656733   )=-0.071868896484375  , -0.0719005546145658 , diff=00.00003165813019075
        /// cos(1.7424924365127     )=-0.170822143554688  , -0.170853764815537  , diff=00.00003162126084999
        /// </remarks>
        public static Fix64 Cos(Fix64 angle)
        {
            // cos(r) = sin(pi/2-r)
            return Sin(pi / 2 - angle);
        }
        //public Fix64 Cos()
        //{
        //    return FromRawValue(Cos(rawValue));
        //}

        /// <summary>
        /// 64位定点数正切
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入数据在接近正切的极值时,
        /// 如 PI / 2 时，计算会产生较大误差。
        /// 
        /// 如果输入的弧度很大的话，可以用 Fix64 表示，
        /// 如 1000 * Math.PI -> 1000 * Fix64.pi，这样在计算时就不会有太大的误差了
        /// 
        /// 测试方法：随机生成 [-2 pi, 2 pi] 的 10000 个样本，取误差最大的 10 个输出
        /// tan(-1.57085361393545   )=21845.1830596924    , 17455.9244758882    , diff=4389.25858380422000000
        /// tan(1.57088707964945    )=-13107.1098175049   , -11018.9371147495   , diff=2088.17270275540000000
        /// tan(-1.57117453720048   )=2730.64775085449    , 2644.03077638488    , diff=86.61697446960990000
        /// tan(-1.57132121229301   )=1927.51597595215    , 1905.17724675282    , diff=22.33872919932920000
        /// tan(-1.57166378528271   )=1170.27737426758    , 1152.79262721611    , diff=17.48474705146760000
        /// tan(-1.57031202995891   )=-2047.98574829102   , -2064.84917411264   , diff=16.86342582162160000
        /// tan(1.57168800828028    )=-1129.92295837402   , -1121.47639191599   , diff=08.44656645803093000
        /// tan(1.56956756996596    )=819.193954467773    , 813.830265815172    , diff=05.36368865260158000
        /// tan(-1.57026358542669   )=-1872.44407653809   , -1877.08326229698   , diff=04.63918575889011000
        /// tan(1.57284357458622    )=-492.747802734375   , -488.45997400099    , diff=04.28782873338486000
        /// </remarks>
        public static Fix64 Tan(Fix64 angle)
        {
            long num = angle.rawValue % pi.rawValue;
            bool flag = false;
            // tan(-r) = -tan(r)
            if (num < 0L)
            {
                num = -num;
                flag = true;
            }
            // tan(r) = -tan(pi-r)
            if (num > pi.rawValue / 2)
            {
                flag = !flag;
                num = pi.rawValue - num;
            }
            long value = TanLut[((int)num)];

            return FromRawValue(flag ? -value : value);
        }
        //public Fix64 Tan()
        //{
        //    return FromRawValue(Tan(rawValue));
        //}
        // cot(r) = -tan(pi/2+r)

        /// <summary>
        /// 64位定点数余切
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入数据在接近余切的极值时,
        /// 如 0 时，计算会产生较大误差。
        /// 
        /// 如果输入的弧度很大的话，可以用 Fix64 表示，
        /// 如 1000 * Math.PI -> 1000 * Fix64.pi，这样在计算时就不会有太大的误差了
        /// 
        /// 测试方法：随机生成 [-2 pi, 2 pi] 的 10000 个样本，取误差最大的 10 个输出
        /// cot(0.00060312162557456 )=1724.61952209473    , 1658.04016361618    , diff=66.57935847854900000
        /// cot(-3.14093492269521   )=1560.36999511719    , 1520.37841621214    , diff=39.99157890504580000
        /// cot(0.000866501007934227)=1191.55516052246    , 1154.06645874465    , diff=37.48870177781280000
        /// cot(3.14147002448174    )=-8191.94360351563   , -8154.67070481764   , diff=37.27289869798190000
        /// cot(0.00113916114259309 )=897.746871948242    , 877.838551586256    , diff=19.90832036198630000
        /// cot(-3.14071081242287   )=1149.74618530273    , 1133.99076647515    , diff=15.75541882758630000
        /// cot(-3.14044449842684   )=885.615142822266    , 870.962037928233    , diff=14.65310489403270000
        /// cot(-0.000864278835370786)=-1170.27737426758   , -1157.03371421602   , diff=13.24366005156250000
        /// cot(0.00192618034832564 )=524.283752441406    , 519.161543801119    , diff=05.12220864028711000
        /// cot(-3.13984562657829   )=574.872650146484    , 572.400412843514    , diff=02.47223730297083000
        /// </remarks>
        public static Fix64 Cot(Fix64 val)
        {
            return -Tan(pi / 2 + val);
        }
        //public Fix64 Cot()
        //{
        //    return FromRawValue(Cot(rawValue));
        //}
        // asin(r) = -asin(-r)

        /// <summary>
        /// 64位定点数反正弦
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，而且它的值域比 sin 更大，
        /// 所以它的误差比 sin 函数大
        /// 
        /// 定义域：[-1, 1]，当输入不在这个范围时，会自取取边界值
        /// 如：asin(-10) = asin(-1)
        /// 
        /// 误差 < 1 / 1000
        /// 
        /// 测试方法：随机生成 [-1, 1] 的 10000 个样本，取误差最大的 10 个输出
        /// asin(0.999960965942573   )=1.56121826171875    , 1.56196068178478    , diff=00.00074242006603420
        /// asin(-0.999719301238525  )=-1.54670715332031   , -1.54710194372956   , diff=00.00039479040924695
        /// asin(-0.999113105237071  )=-1.52835083007813   , -1.5286768327097    , diff=00.00032600263157567
        /// asin(0.99950523301936    )=1.53904724121094    , 1.53933817093399    , diff=00.00029092972304934
        /// asin(-0.999443904030809  )=-1.53718566894531   , -1.53744523933767   , diff=00.00025957039235935
        /// asin(-0.998137753455964  )=-1.50950622558594   , -1.5097582268091    , diff=00.00025200122316749
        /// asin(0.999336901120533   )=1.53414916992188    , 1.53437731594116    , diff=00.00022814601928123
        /// asin(-0.999790637753806  )=-1.55012512207031   , -1.5503332110973    , diff=00.00020808902699199
        /// asin(-0.998164115938434  )=-1.510009765625     , -1.51019193616536   , diff=00.00018217054035796
        /// asin(0.99729653959968    )=1.4970703125        , 1.4972479835694     , diff=00.00017767106939637
        /// </remarks>
        public static Fix64 Asin(Fix64 val)
        {
            bool flag = false;

            if (val < zero)
            {
                val = -val;
                flag = true;
            }

            if (val > one)
            {
                val = one;
            }

            long value = AsinLut[((int)val.rawValue)];

            return FromRawValue(flag ? -value : value);
        }
        //public Fix64 Asin()
        //{
        //    return FromRawValue(Asin(rawValue));
        //}
        // acos(x) = pi/2 - asin(x)

        /// <summary>
        /// 64位定点数反余弦
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，而且它的值域比 cos 更大，
        /// 所以它的误差比 cos 函数大
        /// 
        /// 定义域：[-1, 1]，当输入不在这个范围时，会自取取边界值
        /// 如：acos(-10) = acos(-1)
        /// 
        /// 误差 < 1 / 1000
        /// 
        /// 测试方法：随机生成 [-1, 1] 的 10000 个样本，取误差最大的 10 个输出
        /// acos(0.999960965942573   )=0.0095672607421875  , 0.0088356450101124  , diff=00.00073161573207510
        /// acos(-0.999719301238525  )=3.11749267578125    , 3.11789827052446    , diff=00.00040559474320601
        /// acos(-0.999113105237071  )=3.09913635253906    , 3.0994731595046     , diff=00.00033680696553473
        /// acos(0.99950523301936    )=0.03173828125       , 0.0314581558609098  , diff=00.00028012538909023
        /// acos(-0.999443904030809  )=3.10797119140625    , 3.10824156613257    , diff=00.00027037472631841
        /// acos(-0.998137753455964  )=3.08029174804688    , 3.080554553604      , diff=00.00026280555712654
        /// acos(-0.999790637753806  )=3.12091064453125    , 3.1211295378922     , diff=00.00021889336095082
        /// acos(0.999336901120533   )=0.0366363525390625  , 0.0364190108537405  , diff=00.00021734168532202
        /// acos(-0.998164115938434  )=3.08079528808594    , 3.08098826296025    , diff=00.00019297487431702
        /// acos(-0.99743262585133   )=3.06974792480469    , 3.06992015579958    , diff=00.0001722309948895i
        /// </remarks>
        public static Fix64 Acos(Fix64 val)
        {
            return pi / 2 - Asin(val);
        }

        //public Fix64 Acos()
        //{
        //    return FromRawValue(Acos(rawValue));
        //}

        /// <summary>
        /// 64位定点数反正切
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns>弧度</returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入数据在接近反正切的极值时,
        /// 计算会产生较大误差。
        /// 
        /// 测试方法：随机生成 [-65536, 65536] 的 10000 个样本，取误差最大的 10 个输出
        /// atan(-32769.1950531011   )=-1.57078552246094   , -1.57076581032972   , diff=00.00001971213121710
        /// atan(32769.8931732187    )=1.57078552246094    , 1.57076581097983    , diff=00.00001971148110336
        /// atan(-1724.64416584216   )=-1.57023620605469   , -1.57021649710728   , diff=00.00001970894740899
        /// atan(-32775.8897552527   )=-1.57078552246094   , -1.57076581656292   , diff=00.00001970589801803
        /// atan(16386.6292190564    )=1.57075500488281    , 1.57073530143176    , diff=00.00001970345105740
        /// atan(-32778.9495086721   )=-1.57078552246094   , -1.5707658194109    , diff=00.00001970305003818
        /// atan(21850.9751688666    )=1.57077026367188    , 1.57075056224702    , diff=00.00001970142485663
        /// atan(16387.8903884906    )=1.57075500488281    , 1.57073530612811    , diff=00.00001969875470409
        /// atan(-32786.0958404625   )=-1.57078552246094   , -1.57076582606054   , diff=00.00001969640039357
        /// atan(10924.6996205657    )=1.57072448730469    , 1.57070479109763    , diff=00.00001969620705844
        /// </remarks>
        /// 表示求的是x的反正切，其返回值为[-pi/2,+pi/2]之间的一个数
        public static Fix64 Atan(Fix64 val)
        {
            //[-pi/2,+pi/2]
            // atan(x) = -atan(-x)
            // x>0: atan(x) + atan(1/x) = pi/2
            // x<0: atan(x) + atan(1/x) = -pi/2
            bool flag = false;

            if (val < zero)
            {
                val = -val;
                flag = true;
            }

            bool minus = false;
            if (val > one)
            {
                val = one / val;
                minus = true;
            }

            long value = AtanLut[((int)val.rawValue)];
            value = minus ? pi.rawValue / 2 - value : value;

            return FromRawValue(flag ? -value : value);
        }

        /// <summary>
        /// 求的是y/x的反正切，其返回值为[-pi,+pi]之间的一个数
        /// </summary>
        /// <param name="y"></param>
        /// <param name="x"></param>
        /// <returns>返回弧度</returns>
        public static Fix64 Atan2(Fix64 y, Fix64 x)
        {
            var yl = y.rawValue;
            var xl = x.rawValue;

            //x == 0 情况
            if (xl == 0)
            {
                if (yl > 0)
                {
                    return piOver2;
                }
                if (yl == 0)
                {
                    return zero;
                }
                return -piOver2;
            }

            //x != 0 情况
            Fix64 atan;
            var z = y / x;

            Fix64 sm = Fix64.EN2 * 28;
            // Deal with overflow
            if (one + sm * z * z == maxValue)
            {
                return y < zero ? -piOver2 : piOver2;
            }

            if (Abs(ref z) < one)
            {
                atan = z / (one + sm * z * z);
                if (xl < 0)
                {
                    if (yl < 0)
                    {
                        return atan - pi;
                    }
                    return atan + pi;
                }
            }
            else
            {
                atan = piOver2 - z / (z * z + sm);
                if (yl < 0)
                {
                    return atan - pi;
                }
            }
            return atan;
        }
        

        /// <summary>
        /// 64位定点数反余切
        /// </summary>
        /// <param name="rawValue">用Fix64表示的定点数，单位弧度</param>
        /// <returns></returns>
        /// <remarks>
        /// 由于定点数本身就有 1 / 65536 的误差，所以当输入数据在接近余切的极值时,
        /// 计算会产生较大误差。
        /// 
        /// 测试方法：随机生成 [-65536,65536] 的 10000 个样本，取误差最大的 10 个输出
        /// acot(32769.8931732187    )=0                   , 3.05158150624152E-05, diff=00.00003051581506242
        /// acot(16386.6292190564    )=3.0517578125E-05    , 6.10253631414537E-05, diff=00.00003050778501645
        /// acot(21850.9751688666    )=1.52587890625E-05   , 4.5764547878191E-05 , diff=00.00003050575881569
        /// acot(16387.8903884906    )=3.0517578125E-05    , 6.10206667881474E-05, diff=00.00003050308866315
        /// acot(10924.6996205657    )=6.103515625E-05     , 9.15356972674974E-05, diff=00.00003050054101750
        /// acot(32786.6011505214    )=0                   , 3.05002642724261E-05, diff=00.00003050026427243
        /// acot(32810.7071380814    )=0                   , 3.0477855764488E-05 , diff=00.00003047785576449
        /// acot(32813.6824493621    )=0                   , 3.04750922495156E-05, diff=00.00003047509224952
        /// acot(21868.2629801539    )=1.52587890625E-05   , 4.57283690162846E-05, diff=00.00003046957995378
        /// acot(16397.5472183291    )=3.0517578125E-05    , 6.09847305481637E-05, diff=00.00003046715242316
        /// </remarks>
        public static Fix64 Acot(Fix64 val)
        {
            // acot(x) = pi/2 - atan(x)
            // x>0: acot(x) = atan(1/x) = pi/2 - atan(x)
            // x<0: acot(x) = pi + atan(1/x) = pi/2 - atan(x)
            return pi / 2 - Atan(val);
        }

        internal static void GenerateSinLut()
        {
            using (StreamWriter writer = new StreamWriter("Fix64SinLut.cs"))
            {
                writer.Write("namespace GameFramework.FixedMath {\r\n    partial struct Fix64 {\r\n        public static readonly long[] SinLut = new[] {");
                long range_max = pi.rawValue / 2;
                for (long i = 0; i <= range_max; i++)
                {
                    if ((i % 8) == 0)
                    {
                        writer.WriteLine();
                        writer.Write("            ");
                    }
                    double num = (double)i / range_max * System.Math.PI / 2;
                    long value = Fix64.FromDouble(System.Math.Sin(num)).rawValue;
                    writer.Write(string.Format("0x{0:X}L, ", value));
                }
                writer.Write("\r\n        };\r\n    }\r\n}");
            }
        }
        internal static void GenerateTanLut()
        {
            using (StreamWriter writer = new StreamWriter("Fix64TanLut.cs"))
            {
                writer.Write("namespace GameFramework.FixedMath {\r\n    partial struct Fix64 {\r\n        public static readonly long[] TanLut = new[] {");
                long range_max = pi.rawValue / 2;
                for (long i = 0; i <= range_max; i++)
                {
                    if ((i % 8) == 0)
                    {
                        writer.WriteLine();
                        writer.Write("            ");
                    }
                    double num = (double)i / range_max * System.Math.PI / 2;
                    double tan_value = System.Math.Tan(num);
                    if ((tan_value > maxValue.ToDouble()) || (tan_value < 0.0))
                    {
                        tan_value = maxValue.ToDouble();
                    }

                    long value = Fix64.FromDouble(tan_value).rawValue;
                    writer.Write(string.Format("0x{0:X}L, ", value));
                }
                writer.Write("\r\n        };\r\n    }\r\n}");
            }
        }
        internal static void GenerateAsinLut()
        {
            using (StreamWriter writer = new StreamWriter("Fix64AsinLut.cs"))
            {
                writer.Write("namespace GameFramework.FixedMath {\r\n    partial struct Fix64 {\r\n        public static readonly long[] AsinLut = new[] {");
                long range_max = one.rawValue;
                for (long i = 0; i <= range_max; i++)
                {
                    if ((i % 8) == 0)
                    {
                        writer.WriteLine();
                        writer.Write("            ");
                    }
                    double num = (double)i / range_max;
                    long value = FromDouble(System.Math.Asin(num)).rawValue;
                    writer.Write(string.Format("0x{0:X}L, ", value));
                }
                writer.Write("\r\n        };\r\n    }\r\n}");
            }
        }
        internal static void GenerateAtanLut()
        {
            using (StreamWriter writer = new StreamWriter("Fix64AtanLut.cs"))
            {
                writer.Write("namespace GameFramework.FixedMath {\r\n    partial struct Fix64 {\r\n        public static readonly long[] AtanLut = new[] {");
                long range_max = one.rawValue;
                for (long i = 0; i <= range_max; i++)
                {
                    if ((i % 8) == 0)
                    {
                        writer.WriteLine();
                        writer.Write("            ");
                    }
                    double num = (double)i / range_max;
                    long value = FromDouble(System.Math.Atan(num)).rawValue;
                    writer.Write(string.Format("0x{0:X}L, ", value));
                }
                writer.Write("\r\n        };\r\n    }\r\n}");
            }
        }
    }
}
