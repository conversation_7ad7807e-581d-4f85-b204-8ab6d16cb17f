﻿using System;

namespace FixedMath
{
	public class FRandom
	{
		private const int MBIG = Int32.MaxValue;
		private const int MSEED = 161803398;
		private const int MZ = 0;

		private int inext;
		private int inextp;
		private int[] SeedArray = new int[56];

		public FRandom()
		: this(Environment.TickCount)
		{
		}

		public FRandom(int Seed)
		{
			int ii;
			int mj, mk;

			//Initialize our Seed array.
			//This algorithm comes from Numerical Recipes in C (2nd Ed.)
			int subtraction = (Seed == Int32.MinValue) ? Int32.MaxValue : System.Math.Abs(Seed);
			mj = MSEED - subtraction;
			SeedArray[55] = mj;
			mk = 1;
			for (int i = 1; i < 55; i++)
			{  //Apparently the range [1..55] is special (Knuth) and so we're wasting the 0'th position.
				ii = (21 * i) % 55;
				SeedArray[ii] = mk;
				mk = mj - mk;
				if (mk < 0) mk += MBIG;
				mj = SeedArray[ii];
			}
			for (int k = 1; k < 5; k++)
			{
				for (int i = 1; i < 56; i++)
				{
					SeedArray[i] -= SeedArray[1 + (i + 30) % 55];
					if (SeedArray[i] < 0) SeedArray[i] += MBIG;
				}
			}
			inext = 0;
			inextp = 21;
			Seed = 1;
		}

		//
		// Package Private Methods
		//

		/*====================================Sample====================================
		**Action: Return a new random number [0..1) and reSeed the Seed array.
		**Returns: A fix64 [0..1)
		**Arguments: None
		**Exceptions: None
		==============================================================================*/
		protected virtual long Sample()
		{
			//Including this division at the end gives us significantly improved
			//random number distribution.

			return InternalSample();
		}

		private int InternalSample()
		{
			randomCount++;
			int retVal;
			int locINext = inext;
			int locINextp = inextp;

			if (++locINext >= 56) locINext = 1;
			if (++locINextp >= 56) locINextp = 1;

			retVal = SeedArray[locINext] - SeedArray[locINextp];

			if (retVal == MBIG) retVal--;
			if (retVal < 0) retVal += MBIG;

			SeedArray[locINext] = retVal;

			inext = locINext;
			inextp = locINextp;

			return retVal;
		}

		//
		// Public Instance Methods
		// 

		public int randomCount { get; set; }

		/*=====================================Next=====================================
		**Returns: An int [0..Int32.MaxValue)
		**Arguments: None
		**Exceptions: None.
		==============================================================================*/
		public virtual int Next()
		{
			return InternalSample();
		}

		/*=====================================Next=====================================
		**Returns: An int [minvalue..maxvalue)
		**Arguments: minValue -- the least legal value for the Random number.
		**           maxValue -- One greater than the greatest legal return value.
		**           maxvalue - minvalue < Int32.MaxValue
		**Exceptions: None.
		==============================================================================*/
		public virtual int Next(int minValue, int maxValue)
		{
			if (minValue >= maxValue)
			{
				return 0;
			}

			long range = (long)maxValue - minValue;
			if (range <= (long)Int32.MaxValue)
			{
				long temp = Sample() * range;
				temp = temp / MBIG;
				return (int)(temp + minValue);
			}
			else
			{
				return 0;
			}
		}


		/*=====================================Next=====================================
		**Returns: An int [0..maxValue)
		**Arguments: maxValue -- One more than the greatest legal return value.
		**Exceptions: None.
		==============================================================================*/
		public virtual int Next(int maxValue)
		{
			if (maxValue < 0)
			{
				return 0;
			}
			long temp = Sample() * maxValue;
			temp = temp / MBIG;
			return (int)(temp);
		}


		/*=====================================Next=====================================
		**Returns: A fix64 [0..1)
		**Arguments: None
		**Exceptions: None
		==============================================================================*/
		public virtual Fix64 NextFix64()
		{
			Fix64 fix64_r = Fix64.FromInt(InternalSample());
			Fix64 fix64_m = Fix64.FromInt(MBIG);
			return fix64_r / fix64_m;
		}


		/*==================================NextBytes===================================
		**Action:  Fills the byte array with random bytes [0..0x7f].  The entire array is filled.
		**Returns:Void
		**Arugments:  buffer -- the array to be filled.
		**Exceptions: None
		==============================================================================*/
		public virtual void NextBytes(byte[] buffer)
		{
			if (buffer != null)
			{
				for (int i = 0; i < buffer.Length; i++)
				{
					buffer[i] = (byte)(InternalSample() % (Byte.MaxValue + 1));
				}
			}
		}
	}
}
