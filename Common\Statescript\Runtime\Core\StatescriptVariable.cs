using System;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Variable types supported by the Statescript system
    /// </summary>
    public enum StatescriptVariableType
    {
        <PERSON><PERSON><PERSON>,
        Integer,
        Float,
        String,
        Vector2,
        Vector3,
        Entity,
        Object
    }

    /// <summary>
    /// Represents a variable in a Statescript graph with type-safe value storage
    /// </summary>
    [MemoryPackable]
    public sealed partial class StatescriptVariable
    {
        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;

        [MemoryPackInclude]
        public StatescriptVariableType Type { get; set; }

        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;

        [MemoryPackInclude]
        public bool IsExposed { get; set; } = false;

        // Type-safe value storage - only one will be used based on Type
        [MemoryPackInclude]
        public bool BoolValue { get; set; }

        [MemoryPackInclude]
        public int IntValue { get; set; }

        [MemoryPackInclude]
        public float FloatValue { get; set; }

        [MemoryPackInclude]
        public string StringValue { get; set; } = string.Empty;

        [MemoryPackInclude]
        public FVector2 Vector2Value { get; set; }

        [MemoryPackInclude]
        public FVector3 Vector3Value { get; set; }

        [MemoryPackInclude]
        public int EntityIdValue { get; set; } // Store entity ID instead of reference

        // For complex objects, we'll use a serialized string representation
        [MemoryPackInclude]
        public string ObjectValue { get; set; } = string.Empty;

        [MemoryPackConstructor]
        public StatescriptVariable()
        {
        }

        public StatescriptVariable(string name, StatescriptVariableType type)
        {
            Name = name;
            Type = type;
            SetToDefaultValue();
        }

        public StatescriptVariable(string name, StatescriptVariableType type, bool value) : this(name, type)
        {
            SetValue(value);
        }

        public StatescriptVariable(string name, StatescriptVariableType type, int value) : this(name, type)
        {
            SetValue(value);
        }

        public StatescriptVariable(string name, StatescriptVariableType type, float value) : this(name, type)
        {
            SetValue(value);
        }

        public StatescriptVariable(string name, StatescriptVariableType type, string value) : this(name, type)
        {
            SetValue(value);
        }

        public StatescriptVariable(string name, StatescriptVariableType type, FVector2 value) : this(name, type)
        {
            SetValue(value);
        }

        public StatescriptVariable(string name, StatescriptVariableType type, FVector3 value) : this(name, type)
        {
            SetValue(value);
        }

        /// <summary>
        /// Get the value as a specific type without boxing
        /// </summary>
        public T GetValue<T>(T fallback = default)
        {
            return Type switch
            {
                StatescriptVariableType.Boolean when typeof(T) == typeof(bool) => (T)BoolValue,
                StatescriptVariableType.Integer when typeof(T) == typeof(int) => (T)IntValue,
                StatescriptVariableType.Float when typeof(T) == typeof(float) => (T)FloatValue,
                StatescriptVariableType.String when typeof(T) == typeof(string) => (T)StringValue,
                StatescriptVariableType.Vector2 when typeof(T) == typeof(FVector2) => (T)Vector2Value,
                StatescriptVariableType.Vector3 when typeof(T) == typeof(FVector3) => (T)Vector3Value,
                StatescriptVariableType.Entity when typeof(T) == typeof(int) => (T)EntityIdValue,
                StatescriptVariableType.Object when typeof(T) == typeof(string) => (T)ObjectValue,
                _ => TryConvertValue<T>(fallback)
            };
        }

        /// <summary>
        /// Get the value as an object (for editor/debugging purposes)
        /// </summary>
        public object GetValueAsObject()
        {
            return Type switch
            {
                StatescriptVariableType.Boolean => BoolValue,
                StatescriptVariableType.Integer => IntValue,
                StatescriptVariableType.Float => FloatValue,
                StatescriptVariableType.String => StringValue,
                StatescriptVariableType.Vector2 => Vector2Value,
                StatescriptVariableType.Vector3 => Vector3Value,
                StatescriptVariableType.Entity => EntityIdValue,
                StatescriptVariableType.Object => ObjectValue,
                _ => null
            };
        }

        private T TryConvertValue<T>(T fallback)
        {
            try
            {
                var value = GetValueAsObject();
                if (value != null && typeof(T) != typeof(object))
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
            }
            catch
            {
                // Conversion failed, return fallback
            }

            return fallback;
        }

        /// <summary>
        /// Set the value with type checking and no boxing
        /// </summary>
        public bool SetValue(object newValue)
        {
            if (newValue == null)
            {
                SetToDefaultValue();
                return true;
            }

            return Type switch
            {
                StatescriptVariableType.Boolean when newValue is bool boolVal => SetValue(boolVal),
                StatescriptVariableType.Integer when newValue is int intVal => SetValue(intVal),
                StatescriptVariableType.Float when newValue is float floatVal => SetValue(floatVal),
                StatescriptVariableType.String when newValue is string stringVal => SetValue(stringVal),
                StatescriptVariableType.Vector2 when newValue is FVector2 vec2Val => SetValue(vec2Val),
                StatescriptVariableType.Vector3 when newValue is FVector3 vec3Val => SetValue(vec3Val),
                StatescriptVariableType.Entity when newValue is int entityIdVal => SetEntityValue(entityIdVal),
                StatescriptVariableType.Object => SetObjectValue(newValue),
                _ => TryConvertAndSet(newValue)
            };
        }

        public bool SetValue(bool value)
        {
            if (Type != StatescriptVariableType.Boolean) return false;
            BoolValue = value;
            return true;
        }

        public bool SetValue(int value)
        {
            if (Type != StatescriptVariableType.Integer) return false;
            IntValue = value;
            return true;
        }

        public bool SetValue(float value)
        {
            if (Type != StatescriptVariableType.Float) return false;
            FloatValue = value;
            return true;
        }

        public bool SetValue(string value)
        {
            if (Type != StatescriptVariableType.String) return false;
            StringValue = value ?? string.Empty;
            return true;
        }

        public bool SetValue(FVector2 value)
        {
            if (Type != StatescriptVariableType.Vector2) return false;
            Vector2Value = value;
            return true;
        }

        public bool SetValue(FVector3 value)
        {
            if (Type != StatescriptVariableType.Vector3) return false;
            Vector3Value = value;
            return true;
        }

        public bool SetEntityValue(int entityId)
        {
            if (Type != StatescriptVariableType.Entity) return false;
            EntityIdValue = entityId;
            return true;
        }

        public bool SetObjectValue(object value)
        {
            if (Type != StatescriptVariableType.Object) return false;
            ObjectValue = value?.ToString() ?? string.Empty;
            return true;
        }

        private bool TryConvertAndSet(object newValue)
        {
            try
            {
                return Type switch
                {
                    StatescriptVariableType.Boolean => SetValue(Convert.ToBoolean(newValue)),
                    StatescriptVariableType.Integer => SetValue(Convert.ToInt32(newValue)),
                    StatescriptVariableType.Float => SetValue(Convert.ToSingle(newValue)),
                    StatescriptVariableType.String => SetValue(newValue.ToString()),
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Reset to default value
        /// </summary>
        public void ResetToDefault()
        {
            SetToDefaultValue();
        }

        /// <summary>
        /// Set the variable to its default value based on type
        /// </summary>
        public void SetToDefaultValue()
        {
            switch (Type)
            {
                case StatescriptVariableType.Boolean:
                    BoolValue = false;
                    break;
                case StatescriptVariableType.Integer:
                    IntValue = 0;
                    break;
                case StatescriptVariableType.Float:
                    FloatValue = 0.0f;
                    break;
                case StatescriptVariableType.String:
                    StringValue = string.Empty;
                    break;
                case StatescriptVariableType.Vector2:
                    Vector2Value = FVector2.zero;
                    break;
                case StatescriptVariableType.Vector3:
                    Vector3Value = FVector3.zero;
                    break;
                case StatescriptVariableType.Entity:
                    EntityIdValue = 0;
                    break;
                case StatescriptVariableType.Object:
                    ObjectValue = string.Empty;
                    break;
            }
        }

        /// <summary>
        /// Check if a value is valid for the specified type
        /// </summary>
        public static bool IsValidValueForType(object value, StatescriptVariableType type)
        {
            if (value == null) return true; // Null is valid for all types

            return type switch
            {
                StatescriptVariableType.Boolean => value is bool,
                StatescriptVariableType.Integer => value is int or long or short or byte,
                StatescriptVariableType.Float => value is float or double or decimal,
                StatescriptVariableType.String => value is string,
                StatescriptVariableType.Vector2 => value is FVector2,
                StatescriptVariableType.Vector3 => value is FVector3,
                StatescriptVariableType.Entity => value is int, // Entity stored as ID
                StatescriptVariableType.Object => true, // Object can be anything
                _ => false
            };
        }

        /// <summary>
        /// Get the default value for a variable type as an object
        /// </summary>
        public static object GetDefaultValueForType(StatescriptVariableType type)
        {
            return type switch
            {
                StatescriptVariableType.Boolean => false,
                StatescriptVariableType.Integer => 0,
                StatescriptVariableType.Float => 0.0f,
                StatescriptVariableType.String => string.Empty,
                StatescriptVariableType.Vector2 => FVector2.zero,
                StatescriptVariableType.Vector3 => FVector3.zero,
                StatescriptVariableType.Entity => 0,
                StatescriptVariableType.Object => string.Empty,
                _ => null
            };
        }

        /// <summary>
        /// Create a variable with a default value for the specified type
        /// </summary>
        public static StatescriptVariable CreateWithDefaultValue(string name, StatescriptVariableType type)
        {
            var variable = new StatescriptVariable(name, type);
            variable.SetToDefaultValue();
            return variable;
        }

        public override string ToString()
        {
            var value = GetValueAsObject();
            return $"{Name} ({Type}): {value}";
        }
    }

    /// <summary>
    /// Variable nodes for getting and setting variables
    /// </summary>
    [MemoryPackable]
    public sealed partial class GetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;

        public GetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Get Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                // Store the value in the node's output for other nodes to access
                SetProperty("OutputValue", variable.GetValueAsObject());
                CompleteExecution(true);
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();

            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Get Variable node '{Name}' must specify a variable name");
            }

            return errors;
        }
    }

    /// <summary>
    /// Node for setting variable values
    /// </summary>
    [MemoryPackable]
    public sealed partial class SetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;

        [MemoryPackInclude]
        public object NewValue { get; set; }

        public SetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Set Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                if (variable.SetValue(NewValue))
                {
                    CompleteExecution(true);
                }
                else
                {
                    Context?.LogError($"Failed to set variable '{VariableName}' - invalid value type");
                    CompleteExecution(false);
                }
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();

            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Set Variable node '{Name}' must specify a variable name");
            }

            return errors;
        }
    }
}
