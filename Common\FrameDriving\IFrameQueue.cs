﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common.FrameDriving
{
    /// <summary>
    /// 帧队列,用于存放帧，定义先进先出的规则。
    /// </summary>
    public interface IFrameQueue
    {
        /// <summary>
        /// 入队。
        /// 不允许往队列里面加入null。
        /// </summary>
        /// <param name="frame"></param>
        void Enqueue(Frame frame);

        /// <summary>
        /// 出队。
        /// </summary>
        /// <returns>返回队列最前的frame，返回空，则表示队列是空的.</returns>
        Frame Dequeue();
        
        /// <summary>
        /// 取得队列的长度。
        /// </summary>
        int Count { get; }
    }
}
