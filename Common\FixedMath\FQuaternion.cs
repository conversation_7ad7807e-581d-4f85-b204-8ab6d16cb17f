﻿using System;
using System.Diagnostics;

namespace FixedMath
{
    /// <summary>
    /// q=[w,v]=[w,xi+yj+zk]
    /// The general form to express quaternions is:
    ///         i^2 = j^2 = k^2 = ijk = -1
    /// and
    ///         ij = k, jk = i, ki = j,
    ///         ji = -k, kj = -i, ik = -j
    /// </summary>
    [Serializable]
    public struct FQuaternion
    {
        public Fix64 w;
        public Fix64 x;
        public Fix64 y;
        public Fix64 z;

        public FQuaternion(Fix64 x, Fix64 y, Fix64 z, Fix64 w)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = w;
        }

        public Fix64 magnitude
        {
            get
            {
                // sqrt(w*w + x*x + y*y + z*z)
                return (w * w + x * x + y * y + z * z).Sqrt();
            }
        }

        public Fix64 sqrMagnitude
        {
            get
            {
                //return Fix64.Square(w) + Fix64.Square(x) + Fix64.Square(y) + Fix64.Square(z);
                return w * w + x * x + y * y + z * z;
            }
        }

        public FQuaternion conjugate
        {
            get
            {
                return new FQuaternion(-x, -y, -z, w);
            }
        }

        public FQuaternion normalized
        {
            get
            {
                Fix64 len = magnitude;
                //return new FQuaternion(Fix64.Div(x, len), Fix64.Div(y, len), Fix64.Div(z, len), Fix64.Div(w, len));
                return new FQuaternion(x / len, y / len, z / len, w / len);
            }
        }

        //public static Fix64 Angle(FQuaternion a, FQuaternion b)
        //{
        //    FQuaternion na = a.normalized;
        //    FQuaternion nb = b.normalized;

        //    return ACos(Dot(na, nb));
        //}

        /// <summary>
        /// q^-1 = q^*/|q|^2
        /// q*q-1 = identity
        /// 如果是单位四元数，可以调用conjugate接口计算逆四元数
        /// </summary>
        /// <param name="q"></param>
        /// <returns></returns>
        public static FQuaternion Inverse(FQuaternion q)
        {
            Fix64 sqrLen = q.sqrMagnitude;

            if (sqrLen == Fix64.one)
            {
                return q.conjugate; ;
            }
            else
            {
                //return new FQuaternion(Fix64.Div(-q.x, sqrLen), Fix64.Div(-q.y, sqrLen),
                //Fix64.Div(-q.z, sqrLen), Fix64.Div(q.w, sqrLen));

                return new FQuaternion(-q.x / sqrLen, -q.y / sqrLen,
                   -q.z / sqrLen, q.w / sqrLen);
            }
        }

        /// <summary>
        /// builds a Quaternion by rotate axis and angle.
        /// </summary>
        /// <param name="axis">rotate axis.</param>
        /// <param name="angle"></param>
        /// <returns></returns>
        public static FQuaternion AngleAxis(Fix64 angle, FVector3 axis)
        {
            Fix64 angle_over_2 = angle / 2;

            Fix64 sin_theta = Fix64.Sin(angle_over_2);
            Fix64 cos_theta = Fix64.Sin(angle_over_2);

            FVector3 v = axis.normalized * sin_theta;

            return new FQuaternion(v.x, v.y, v.z, cos_theta);
        }

        public static FQuaternion identity { get { return _identiy; } }
        static FQuaternion _identiy = new FQuaternion( Fix64.zero, Fix64.zero, Fix64.zero, Fix64.one);

        public static FQuaternion operator +(FQuaternion a, FQuaternion b)
        {
            return new FQuaternion(a.x + b.x, a.y + b.y, a.z + b.z, a.w + b.w);
        }

        public static FQuaternion operator -(FQuaternion a, FQuaternion b)
        {
            return new FQuaternion(a.x - b.x, a.y - b.y, a.z - b.z, a.w - b.w);
        }

        /// <summary>
        /// a.b = [a.w*b.w + a.xi * b.xi + a.yi * b.yi + a.zi * b.zi]
        /// a.b = a.w*b.w - (ax*bx + ay*by + az*bz)
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static Fix64 Dot(FQuaternion a, FQuaternion b)
        {
            return a.w * b.w - (a.x * b.x + a.y * b.y + a.z * b.z);
            //return Fix64.Mul(a.w, b.w) - (Fix64.Mul(a.x, b.x) + Fix64.Mul(a.y, b.y) + Fix64.Mul(a.z, b.z));
        }

        /// <summary>
        /// [wa,va]*[wb,vb] = [wa*wb - va.vb, wa * vb + wb*va + vaxvb]
        /// 四元数乘法满足结合律，不满足交换律.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        /// [w1, v1] x [w2, v2] = [w1w2 - v1v2, w1v2 + w2v1 + v1xv2]  (所以q~1 p q是变换公式)
        public static FQuaternion operator *(FQuaternion a, FQuaternion b)
        {
            FQuaternion q = new FQuaternion();

            q.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;
            q.x = a.w * b.x + b.w * a.x + a.y * b.z - b.y * a.z;
            q.y = a.w * b.y + b.w * a.y + a.z * b.x - b.z * a.x;
            q.z = a.w * b.z + b.w * a.z + a.x * b.y - b.x * a.y;

            return q;
        }

        /// <summary>
        /// 四元数乘向量=四元数表达的旋转应用到向量v上(v'=q*v*q^-1)  christtang 这里公式不对吧？？
        /// </summary>
        /// <param name="q">q为单位四元数</param>
        /// <param name="v"></param>
        /// <returns></returns>
        public static FVector3 operator *(FQuaternion q, FVector3 v)
        {
            FQuaternion pq = new FQuaternion(v.x, v.y, v.z, Fix64.zero);
            FQuaternion tq = q * pq * Conjugate(q);

            return new FVector3(tq.x, tq.y, tq.z);
        }

        /// <summary>
        /// q=[w,v]
        /// aq = [aw,av]
        /// </summary>
        /// <param name="q"></param>
        /// <param name="a"></param>
        /// <returns></returns>
        public static FQuaternion operator *(FQuaternion q, Fix64 a)
        {
            //return new FQuaternion(Fix64.Mul(a, q.x), Fix64.Mul(a, q.y), Fix64.Mul(a, q.z), Fix64.Mul(a, q.w));
            return new FQuaternion(a * q.w, a * q.x, a * q.y, a * q.z);
        }

        public static bool operator ==(FQuaternion a, FQuaternion b)
        {
            return a.w == b.w && a.x == b.x && a.y == b.y && a.z == b.z;
        }

        public static bool operator !=(FQuaternion a, FQuaternion b)
        {
            return a.w != b.w || a.x != b.x || a.y != b.y || a.z != b.z;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is FQuaternion)) return false;
            FQuaternion other = (FQuaternion)obj;

            return this == other;
        }

        public override int GetHashCode()
        {
            return x.GetHashCode() ^
                y.GetHashCode() ^
                z.GetHashCode() ^
                w.GetHashCode();
        }

        public static FQuaternion operator -(FQuaternion q)
        {
            return new FQuaternion(-q.x, -q.y, -q.z, -q.w);
        }

        public static FQuaternion Conjugate(FQuaternion q)
        {
            return new FQuaternion(-q.x, -q.y, -q.z, q.w);
        }

        /// <summary>
        /// Multiply two quaternions.
        /// </summary>
        /// <param name="quaternion1">The first quaternion.</param>
        /// <param name="quaternion2">The second quaternion.</param>
        /// <param name="result">The product of both quaternions.</param>
        public static void Multiply(ref FQuaternion quaternion1, ref FQuaternion quaternion2, out FQuaternion result)
        {
            result = quaternion1 * quaternion2;
        }

        public void Normalize()
        {
            Fix64 len = magnitude;
            x = x / len;
            y = y / len;
            z = z / len;
            w = w / len;
        }

        public void SetAngleAxis(Fix64 angle, FVector3 axis)
        {
            Fix64 angle_over_2 = angle / 2;

            Fix64 sin_theta = Fix64.Sin(angle_over_2);
            Fix64 cos_theta = Fix64.Cos(angle_over_2);

            FVector3 v = axis.normalized * sin_theta;

            w = cos_theta;
            x = v.x;
            y = v.y;
            z = v.z;
        }

        public void ToAngleAxis(out Fix64 angle, FVector3 axis)
        {
            angle = Fix64.Acos(w) * 2;
            axis = new FVector3(x, y, z);
            axis.Normalize();
        }

        /// <summary>
        /// caculates angle betweens a and b.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static Fix64 Angle(FQuaternion a, FQuaternion b)
        {
            FQuaternion ia = Inverse(a);
            FQuaternion d = ia * b;

            return Fix64.Acos(d.w) * 2;
        }

        /// <summary>
        /// 在两个朝向之间构造一个四元数.
        /// </summary>
        /// <param name="fromDir">单位向量</param>
        /// <param name="toDir">单位向量</param>
        /// <returns></returns>
        public static FQuaternion FromToRotation(FVector3 fromDir, FVector3 toDir)
        {
            FQuaternion q = new FQuaternion();
            q.SetFromToRotation(fromDir, toDir);

            return q;
        }

        public void SetFromToRotation(FVector3 fromDir, FVector3 toDir)
        {
            Debug.Assert(Fix64.one - fromDir.magnitude < Fix64.epsilon ||
              Fix64.one - fromDir.magnitude > Fix64.epsilon,
              "fromDir.magnitude != 1");

            Debug.Assert(Fix64.one - toDir.magnitude < Fix64.epsilon ||
               Fix64.one - toDir.magnitude > Fix64.epsilon,
               "toDir.magnitude != 1");

            Fix64 cos_theta = FVector3.Dot(fromDir, toDir);
            Fix64 angle = Fix64.Acos(cos_theta);
            FVector3 axis = FVector3.Cross(fromDir, toDir);

            SetAngleAxis(angle, axis);
        }



        public static void CreateFromMatrix(ref FMatrix3x3 matrix, out FQuaternion result)
        {
            MatrixToQuaternion(ref matrix, out result);
        }

        public static FQuaternion MatrixToQuaternion(FMatrix3x3 matrix)
        {
            FQuaternion result;
            FQuaternion.MatrixToQuaternion(ref matrix, out result);
            return result;
        }

        /// <summary>
        /// quaternion from a matrix.
        /// </summary>
        /// <param name="matrix">A matrix representing an orientation.</param>
        /// <param name="result">A quaternion</param>
        public static void MatrixToQuaternion(ref FMatrix3x3 matrix, out FQuaternion result)
        {
            Fix64 trace = matrix.m00 + matrix.m11 + matrix.m22;
            if (trace > Fix64.zero)  //  即trace + 1 >= 1
            {
                Fix64 root = (trace + Fix64.one).Sqrt();  //  root = 2w
                result.w = root * Fix64.half;     //=>  w = 2w * 1/2 
                root = Fix64.half / root;          //1/4w = （1/2） / （2w）
                result.x = (matrix.m12 - matrix.m21) * root;
                result.y = (matrix.m20 - matrix.m02) * root;
                result.z = (matrix.m01 - matrix.m10) * root;
            }
            else if ((matrix.m00 >= matrix.m11) && (matrix.m00 >= matrix.m22))  //m00 - m11 - m22 > 0
            {
                Fix64 root = (Fix64.one + matrix.m00 - matrix.m11 - matrix.m22).Sqrt();  //  root = 2x
                result.x = Fix64.half * root;
                root = Fix64.half / root;
                result.y = (matrix.m01 + matrix.m10) * root;
                result.z = (matrix.m02 + matrix.m20) * root;
                result.w = (matrix.m12 - matrix.m21) * root;
            }
            else if (matrix.m11 > matrix.m22)          //m11 - m00 - m33 > 0
            {
                Fix64 root = (Fix64.one + matrix.m11 - matrix.m00 - matrix.m22).Sqrt();  //  root = 2y
                result.y = Fix64.half * root;
                root = Fix64.half / root;
                result.x = (matrix.m10 + matrix.m01) * root;
                result.z = (matrix.m21 + matrix.m12) * root;
                result.w = (matrix.m20 - matrix.m02) * root;
            }
            else  //m22 - m00 - m11 > 0
            {
                Fix64 root = (Fix64.one + matrix.m22 - matrix.m00 - matrix.m11).Sqrt();  //  root = 2z
                result.z = Fix64.half * root;
                root = Fix64.half / root;

                result.x = (matrix.m20 + matrix.m02) * root;
                result.y = (matrix.m21 + matrix.m12) * root;
                result.w = (matrix.m01 - matrix.m10) * root;
            }
        }

        public static FQuaternion LookRotation(FVector3 forward)
        {
            return MatrixToQuaternion(FMatrix3x3.LookAt(forward, FVector3.up));
        }

        public static FQuaternion LookRotation(FVector3 forward, FVector3 upwards)
        {
            return MatrixToQuaternion(FMatrix3x3.LookAt(forward, upwards));
        }


        public override string ToString()
        {
            return string.Format("{0},{1},{2},{3}",
                x.ToString(), y.ToString(), z.ToString(), w.ToString());
        }
    }
}
