﻿
using FixedMath;

namespace Common.Physics
{
    /// <summary>
    /// detrministic math operation For Jitter Physics
    /// </summary>
    public sealed class DMath
    {
        /// <summary>
        /// PI constant.
        /// </summary>
        public static readonly Fix64 Pi = Fix64.pi;

        ///**
        //*  @brief PI over 2 constant.
        //**/
        //public static FP PiOver2 = FP.PiOver2;

        /// <summary>
        /// A small value often used to decide if numeric 
        /// results are zero.
        /// </summary>
		public static readonly Fix64 Epsilon = Fix64.EN2;

        ///**
        //*  @brief Degree to radians constant.
        //**/
        //public static FP Deg2Rad = FP.Deg2Rad;

        ///**
        //*  @brief Radians to degree constant.
        //**/
        //public static FP Rad2Deg = FP.Rad2Deg;


        public static void Absolute(ref FMatrix3x3 matrix, out FMatrix3x3 result)
        {
            result.m00 = Fix64.Abs(ref matrix.m00);
            result.m01 = Fix64.Abs(ref matrix.m01);
            result.m02 = Fix64.Abs(ref matrix.m02);
            result.m10 = Fix64.Abs(ref matrix.m10);
            result.m11 = Fix64.Abs(ref matrix.m11);
            result.m12 = Fix64.Abs(ref matrix.m12);
            result.m20 = Fix64.Abs(ref matrix.m20);
            result.m21 = Fix64.Abs(ref matrix.m21);
            result.m22 = Fix64.Abs(ref matrix.m22);
        }

        /// <summary>
        /// Returns a number which is within [min,max]
        /// </summary>
        /// <param name="value">The value to clamp.</param>
        /// <param name="min">The minimum value.</param>
        /// <param name="max">The maximum value.</param>
        /// <returns>The clamped value.</returns>
        public static Fix64 Clamp(Fix64 value, Fix64 min, Fix64 max)
        {
            if (value < min)
            {
                value = min;
                return value;
            }
            if (value > max)
            {
                value = max;
            }
            return value;
        }

    }
}
