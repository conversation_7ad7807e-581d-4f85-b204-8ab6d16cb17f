﻿
using UnityEngine;
using System.Collections;

namespace Common.FrameDriving
{
    /// <summary>
    /// FrameDriver负责驱动整个游戏的逻辑帧。
    /// 运行期只有一个实例。
    /// </summary>
    public class FrameDriver
    {
        public delegate void FrameDelegate(Frame frame);
        public FrameDelegate onFrameCallback { get; set; }

        public int frameAccuTime { get; set; }
        public int totalTime { get; set; }

        /// <summary>
        /// 设置查询最大缓存的帧数量
        /// </summary>
        private int _maxFrameCount;
        /// <summary>
        /// 设置查询最大缓存的帧数量
        /// </summary>
        public int FrameCount
        {
            get
            {
                return _maxFrameCount;
            }
            set
            {
                _maxFrameCount = value;
            }
        }

        public FrameDriver()
        {
            _maxFrameCount = 50;
        }

        /// <summary>
        /// 执行一帧的回调。
        /// </summary>
        /// <param name="frame"></param>
        public void DoFrame(Frame frame)
        {
            TriggerOnFrame(frame);
        }

        public void Reset()
        {
            frameAccuTime = 0;
            totalTime = 0;
        }

        void TriggerOnFrame(Frame frame)
        {
            if (null != onFrameCallback)
            {
                onFrameCallback(frame);
            }
        }

        public void Update(int elapsedTime)
        {
            totalTime += elapsedTime;

            // 产生帧
            if (null != frameGenerator)
            {
                frameGenerator.GenerateFrames(elapsedTime);
            }

            // 消费帧
            if (null != frameDispatcher)
            {
                frameDispatcher.DispatchFrames(this);
            }
        }

        /// <summary>
        /// 可插的帧队列。
        /// </summary>
        private IFrameQueue _frameQueue = null;
        public IFrameQueue frameQueue {
            get 
            {
                if(_frameQueue == null)
                {
                    _frameQueue = new DefaultFrameQueue();
                }
                return _frameQueue;
            }
            set
            {
                _frameQueue = value;
            } 
        }

        /// <summary>
        /// 可插的帧产生器。
        /// </summary>
        private IFrameGenerator _frameGenerator = null;
        public IFrameGenerator frameGenerator { 
            get
            {
                if(_frameGenerator == null)
                {
                    _frameGenerator = new DefaultFrameGenerator(this);
                }
                return _frameGenerator;
            }
            set 
            {
                _frameGenerator = value;
            } 
        }

        /// <summary>
        /// 可插的帧分发器。
        /// </summary>
        IFrameDispatcher _frameDispatcher = null;
        public IFrameDispatcher frameDispatcher
        {
            get
            {
                if(_frameDispatcher == null)
                {
                    _frameDispatcher = new DefaultFrameDispatcher();
                }
                return _frameDispatcher;
            }
            set
            {
                _frameDispatcher = value;
            }
        }

        /// <summary>
        /// 可插的帧产生器。
        /// </summary>
        private IFramePool _framePool = null;
        public IFramePool framePool
        { 
            get
            {
                if(_framePool == null)
                {
                    _framePool = new DefaultFramePool();
                }
                return _framePool;
            }
            set
            {
                _framePool = value;
            }
        }

        public bool IsFrameQueueFull(int addCount = 0)
        {
            return frameQueue.Count + addCount >= _maxFrameCount;
        }
    }
}

