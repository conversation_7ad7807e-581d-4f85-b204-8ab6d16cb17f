﻿
using Common.Pool;

namespace Common.FrameDriving
{
    public class DefaultFrameGenerator : <PERSON>ame<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IFrameGenerator
    {
        public delegate object ConvertFrameDataDelegate(FrameInfo frameInfo);
        public ConvertFrameDataDelegate ConvertFrameData;

        public delegate void DestroyFrameDataDelegate(object frameData);
        public DestroyFrameDataDelegate DestroyFrameData;

        public int frameInterval { get; set; }
        public FrameDriver frameDriver { get ; }

        public DefaultFrameGenerator(FrameDriver frameDriver)
        {
            frameInterval = 66;
            this.frameDriver = frameDriver;
        }

        public DefaultFrameGenerator(FrameDriver frameDriver, int frameInterval)
        {
            this.frameInterval = frameInterval;
            this.frameDriver = frameDriver;
        }

        public virtual void GenerateFrames(int elapsedTime)
        {
            FrameInfoPoolable frameInfoPoolable = FS_ClassPool<FrameInfoPoolable>.Get();
            bool hasData = (LockSteper.Instance as LockStepImpl).ReadFrame(frameInfoPoolable.frameInfo);
            if (hasData)
            {
                frameInfoPoolable.TimeSpan = frameInterval;
                OnSyncFrameReceived(frameInfoPoolable);

                int frameId = frameInfoPoolable.frameInfo.FrameId;
                while (frameId < LockSteper.Instance.GetCurrentMaxFrameId() 
                       && hasData && !IsQueueFull(frameDriver))
                {
                    frameInfoPoolable = FS_ClassPool<FrameInfoPoolable>.Get();
                    hasData = (LockSteper.Instance as LockStepImpl).ReadFrame(frameInfoPoolable.frameInfo);
                    if (hasData)
                    {
                        frameId = frameInfoPoolable.frameInfo.FrameId;
                        frameInfoPoolable.TimeSpan = frameInterval;
                        OnSyncFrameReceived(frameInfoPoolable);
                    }
                    else
                    {
                        frameInfoPoolable.Release();
                    }
                }
            }
            else
            {
                frameInfoPoolable.Release();
            }
        }

        /// <summary>
        /// 帧被dispath之后的回调。
        /// </summary>
        /// <param name="frame"></param>
        public virtual void OnFrameDispatched(Frame frame)
        {
            if (null != DestroyFrameData)
            {
                DestroyFrameData(frame.data);
            }
        }

        public virtual void OnSyncFrameReceived(FrameInfoPoolable frameInfoPoolable)
        {
            object frameData = null;

            if (null != ConvertFrameData) //将Frame数据转换成游戏对应的数据
            {
                frameData = ConvertFrameData(frameInfoPoolable.frameInfo);
            }
            
            GenerateOneFrame(frameDriver, frameInfoPoolable.TimeSpan, frameData);
            frameInfoPoolable.Release();
        }

        public virtual bool IsQueueFull(FrameDriver frameDriver)
        {
            return frameDriver.IsFrameQueueFull();
        }

        public virtual void ConsumeFrames(int elapsedTime)
        {
        }
    }
}

