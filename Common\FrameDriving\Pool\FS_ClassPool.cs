﻿using System;
using System.Collections.Generic;

namespace Common.Pool
{
    /// <summary>
    /// 对象池接口
    /// </summary>
    public interface FS_IClassPool
    {
        /// <summary>
        /// 将对象放回Pool中
        /// </summary>
        /// <param name="objToRelease"></param>
        void Release(FS_PoolableObject objToRelease);

        /// <summary>
        /// 从Pool中获取一个
        /// </summary>
        /// <returns></returns>
        FS_PoolableObject Obtain();
    }

    /// <summary>
    /// 类型对象池的基类
    /// </summary>
    public abstract class FS_BaseClassPool : FS_IClassPool
    {
        /// <summary>
        /// The pool.
        /// </summary>
        protected List<object> Pool = new List<object>(128);

        /// <summary>
        /// The req seq.
        /// </summary>
        public uint ReqSeq;

        /// <summary>
        /// Gets or sets the capacity.
        /// </summary>
        public int Capacity
        {
            get
            {
                return this.Pool.Capacity;
            }

            set
            {
                this.Pool.Capacity = value;
            }
        }

        /// <summary>
        /// The release.
        /// </summary>
        /// <param name="objToRelease">
        /// The obj.
        /// </param>
        public abstract void Release(FS_PoolableObject objToRelease);
        public abstract FS_PoolableObject Obtain();
    }

    /// <summary>
    /// 可池化的基类
    /// </summary>
    public abstract class FS_PoolableObject
    {
        /// <summary>
        /// Pool分配器
        /// </summary>
        public FS_IClassPool PoolHolder;

        /// <summary>
        /// 对象SeqNum，用于跟踪Handle有效性；
        /// 0是非法值：表示已释放、未使用的意思
        /// </summary>
        public uint UsingSeq = 0;

        /// <summary>
        /// The on reuse.对象从池中被启用;需要做一些重初始化的的工作
        /// </summary>
        public abstract void OnReuse();

        /// <summary>
        /// 重置所有字段
        /// </summary>
        protected abstract void ResetAllFields();


        /// <summary>
        /// 对象还回池中善后
        /// </summary>
        protected abstract void OnRelease();


        /// <summary>
        /// The release.
        /// </summary>
        public void Release()
        {
            if (this.PoolHolder != null)
            {
                this.OnRelease();
                this.PoolHolder.Release(this);
            }
        }
    }

    /// <summary>
    /// Pool对象分配器
    /// </summary>
    /// <typeparam name="T">
    /// </typeparam>
    public class FS_ClassPool<T> : FS_BaseClassPool
        where T : FS_PoolableObject, new()
    {
        /// <summary>
        /// The instance.
        /// </summary>
        private static FS_ClassPool<T> instance = null;

        /// <summary>
        /// 从库中取一个，如果没有，则new一个
        /// 都会调用对象的OnReuse
        /// </summary>
        /// <returns>
        /// The <see cref="PoolableObject"/>.
        /// </returns>
        public override FS_PoolableObject Obtain()
        {
            this.ReqSeq++;

            // 从池子中取 一个对象
            if (this.Pool.Count > 0)
            {
                T instanceToReuse = (T)this.Pool[this.Pool.Count - 1];
                this.Pool.RemoveAt(this.Pool.Count - 1);

                instanceToReuse.OnReuse();
                instanceToReuse.UsingSeq = this.ReqSeq;
                instanceToReuse.PoolHolder = this;
                return instanceToReuse;
            }

            // 池子是空的new 一个
            var newInstance = this.NewInstance();
            newInstance.OnReuse();
            return newInstance;
        }

        /// <summary>
        /// 预分配
        /// </summary>
        /// <param name="objToRelease">
        /// The obj.
        /// </param>
        public void Prealloc(int count)
        {
            for (int index = 0; index < count; index++)
            {
                T newObject = this.NewInstance();
                this.Release(newObject);
            }
        }

        private T NewInstance()
        {
            var newInstance = new T();
            newInstance.UsingSeq = this.ReqSeq;
            newInstance.PoolHolder = this;
            return newInstance;
        }

        /// <summary>
        /// The get instance.
        /// </summary>
        /// <returns>
        /// The <see cref="ClassPool"/>.
        /// </returns>
        public static FS_ClassPool<T> GetInstance()
        {
            return Instance;
        }

        /// <summary>
        /// Gets the instance.
        /// </summary>
        public static FS_ClassPool<T> Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FS_ClassPool<T>();
                }

                return instance;
            }
        }

        /// <summary>
        /// The new seq.
        /// </summary>
        /// <returns>
        /// The <see cref="uint"/>.
        /// </returns>
        public static uint NewSeq()
        {
            Instance.ReqSeq++;
            return Instance.ReqSeq;
        }

        /// <summary>
        /// 从Pool中获取一个对象或者new一个
        /// 都会调用对象的OnReuse
        /// </summary>
        /// <returns>
        /// The <see cref="T"/>.
        /// </returns>
        public static T Get()
        {
            return Instance.Obtain() as T;
        }

        /// <summary>
        /// 释放对象请调用Poolable.Release
        /// 不要直接调用ClassPool<T>.Release
        /// </summary>
        /// <param name="objToRelease">
        /// The obj.
        /// </param>
        /// <exception cref="InvalidOperationException">
        /// </exception>
        public override void Release(FS_PoolableObject objToRelease)
        {
            // 等于0表示已释放、未使用的意思
            objToRelease.UsingSeq = 0;

            var objectToPool = objToRelease as T;

            objToRelease.PoolHolder = null;
            this.Pool.Add(objectToPool);
        }
    }
}

