﻿using FixedMath;

namespace Common.Physics {

	public class ConstraintHierarchy : Constraint
	{

		private RigidBody parent;

		private RigidBody child;

		private FVector3 childOffset;

		public ConstraintHierarchy(IBody parent, IBody child, FVector3 childOffset) : base((RigidBody) parent, (RigidBody) child) {
			this.parent = (RigidBody) parent;
			this.child = (RigidBody) child;

			this.childOffset = childOffset;
		}

		public override void PostStep() {
			child.Position = childOffset + parent.Position;
		}

	}

}