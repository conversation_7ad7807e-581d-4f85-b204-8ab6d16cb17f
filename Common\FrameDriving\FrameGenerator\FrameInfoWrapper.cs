﻿using System;
// using GCloud.LockStep;
// using GCloud;

namespace Common.FrameDriving
{
    public class FrameInfoWrapper : FrameInfo
    {
        public long Ticks;
        public FrameInfoWrapper()
        {
        }
        public FrameInfoWrapper(FrameInfo frameInfo)
        {
            Ticks = DateTime.Now.Ticks;
            frameInfo.CopyTo(this);
        }
        
        // public override void WriteTo(ApolloBufferWriter writer)
        // {
        //     base.WriteTo(writer);
        //     writer.Write(Ticks);
        // }

        // public override void ReadFrom(ApolloBufferReader reader)
        // {
        //     base.ReadFrom(reader);
        //     reader.Read(ref Ticks);
        // }
    }
}