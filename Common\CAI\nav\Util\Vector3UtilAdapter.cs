﻿/*
 * Copyright (c) 2010-2012 <PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
using System;
using UnityEngine;

namespace dtnmUtil
{
    /// <summary>
    /// Provides various 3D vector utility methods.
    /// </summary>
    /// <remarks>
    /// <para>
    /// Static methods are thread safe.
    /// </para>
    /// </remarks>
    public static class Vector3UtilAdapter
    {
        /// <summary>
        /// Flattens the vector array into a float array in the form (x, y, z) * vertCount.
        /// </summary>
        /// <param name="vectors">An array of vectors.</param>
        /// <param name="count">The number of vectors.</param>
        /// <returns>An array of flattened vectors.</returns>
        public static float[] Flatten(Vector3[] vectors, int count)
        {
            float[] result = new float[count * 3];
            for (int i = 0; i < count; i++)
            {
                result[i * 3] = vectors[i].x;
                result[i * 3 + 1] = vectors[i].y;
                result[i * 3 + 2] = vectors[i].z;
            }
            return result;
        }

        /// <summary>
        /// Creates an array of vectors from a flattend array of vectors.
        /// </summary>
        /// <param name="flatVectors">An array of vectors. [(x, y, z) * vertCount]</param>
        /// <returns>An array of vectors.</returns>
        public static Vector3[] GetVectors(float[] flatVectors)
        {
            int count = flatVectors.Length / 3;
            Vector3[] result = new Vector3[count];
            for (int i = 0; i < count; i++)
            {
                int p = i * 3;
                result[i] = new Vector3(flatVectors[p + 0]
                    , flatVectors[p + 1]
                    , flatVectors[p + 2]);
            }
            return result;
        }

        /// <summary>
        /// Copies a range of vectors from a flattend array to a vector array.
        /// </summary>
        /// <remarks>
        /// <para>
        /// If a target buffer is provided is must be large enough to contain the results.
        /// </para>
        /// <para>
        /// If the target buffer is null a new array will be created with a length of
        /// <paramref name="count"/> + <paramref name="targetIndex"/>
        /// </para>
        /// </remarks>
        /// <param name="source">An array of vectors in the form (x, y, z) * vertCount.</param>
        /// <param name="sourceIndex">The index of the start vector in the source array.</param>
        /// <param name="target">The target vector buffer. (Or null.)</param>
        /// <param name="targetIndex">The index within the target buffer to start the copy.</param>
        /// <param name="count">The number of vectors to copy.</param>
        /// <returns>
        /// The reference to the <paramref name="target"/> buffer, or a new array if no buffer 
        /// was provided.
        /// </returns>
        public static Vector3[] GetVectors(float[] source
            , int sourceIndex
            , Vector3[] target
            , int targetIndex
            , int count)
        {
            if (target == null)
                target = new Vector3[count + targetIndex];

            for (int i = 0; i < count; i++)
            {
                int p = (sourceIndex + i) * 3;
                target[targetIndex + i] = new Vector3(source[p + 0], source[p + 1], source[p + 2]);
            }

            return target;
        }

        /// <summary>
        /// Gets the minimum and maximum bounds of the AABB which contains the array of points.
        /// </summary>
        /// <param name="vectors">An array of points.</param>
        /// <param name="count">The number of points in the array.</param>
        /// <param name="boundsMin">The mimimum bounds of the AABB.</param>
        /// <param name="boundsMax">The maximum bounds of the AABB.</param>
        public static void GetBounds(Vector3[] vectors
            , int count
            , out Vector3 boundsMin
            , out Vector3 boundsMax)
        {
            boundsMin = vectors[0];
            boundsMax = vectors[0];

            for (int i = 1; i < count; i++)
            {
                boundsMin.x = Math.Min(boundsMin.x, vectors[i].x);
                boundsMin.y = Math.Min(boundsMin.y, vectors[i].y);
                boundsMin.z = Math.Min(boundsMin.z, vectors[i].z);
                boundsMax.x = Math.Max(boundsMax.x, vectors[i].x);
                boundsMax.y = Math.Max(boundsMax.y, vectors[i].y);
                boundsMax.z = Math.Max(boundsMax.z, vectors[i].z);
            }
        }

        /// <summary>
        /// Returns a standard string representation of the specified vector.
        /// </summary>
        /// <param name="vector">A vector.</param>
        /// <returns>A string representing the vector.</returns>
        public static string ToString(Vector3 vector)
        {
            return string.Format("[{0:F3}, {1:F3}, {2:F3}]"
                , vector.x, vector.y, vector.z);
        }
    }
}
