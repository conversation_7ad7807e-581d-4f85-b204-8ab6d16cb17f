﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net7.0;net8.0;netstandard2.1</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
        <RootNamespace>MemoryPack</RootNamespace>
        <NoWarn>$(NoWarn);CS1591;CA2255</NoWarn>
        <PackageTags>serializer</PackageTags>
        <Description>Core libraries(attribute, logics) of MemoryPack.</Description>
    </PropertyGroup>

    <ItemGroup>
        <None Include="../../Icon.png" Pack="true" PackagePath="/" />
        <InternalsVisibleTo Include="MemoryPack.Streaming, PublicKey=00240000048000009400000006020000002400005253413100040000010001000144ec28f1e9ef7b17dacc47425a7a153aea0a7baa590743a2d1a86f4b3e10a8a12712c6e647966bfd8bd6e830048b23bd42bbc56f179585c15b8c19cf86c0eed1b73c993dd7a93a30051dd50fdda0e4d6b65e6874e30f1c37cf8bcbc7fe02c7f2e6a0a3327c0ccc1631bf645f40732521fa0b41a30c178d08f7dd779d42a1ee" />
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework) == 'netstandard2.1'">
        <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
        <PackageReference Include="System.Collections.Immutable" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Formatters\TupleFormatter.tt">
            <Generator>TextTemplatingFileGenerator</Generator>
            <LastGenOutput>TupleFormatter.cs</LastGenOutput>
        </None>
        <None Update="MemoryPackFormatterProvider.WellknownTypes.tt">
            <Generator>TextTemplatingFileGenerator</Generator>
            <LastGenOutput>MemoryPackFormatterProvider.WellknownTypes.cs</LastGenOutput>
        </None>
        <None Update="MemoryPackReader.Unmanaged.tt">
            <Generator>TextTemplatingFileGenerator</Generator>
            <LastGenOutput>MemoryPackReader.Unmanaged.cs</LastGenOutput>
        </None>
        <None Update="MemoryPackWriter.Unmanaged.tt">
            <Generator>TextTemplatingFileGenerator</Generator>
            <LastGenOutput>MemoryPackWriter.Unmanaged.cs</LastGenOutput>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Formatters\TupleFormatter.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>TupleFormatter.tt</DependentUpon>
        </Compile>
        <Compile Update="MemoryPackFormatterProvider.WellknownTypes.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>MemoryPackFormatterProvider.WellknownTypes.tt</DependentUpon>
        </Compile>
        <Compile Update="MemoryPackReader.Unmanaged.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>MemoryPackReader.Unmanaged.tt</DependentUpon>
        </Compile>
        <Compile Update="MemoryPackWriter.Unmanaged.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>MemoryPackWriter.Unmanaged.tt</DependentUpon>
        </Compile>
    </ItemGroup>

</Project>
