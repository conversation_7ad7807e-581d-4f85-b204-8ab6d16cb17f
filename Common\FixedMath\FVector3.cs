﻿using System;

namespace FixedMath
{
    [Serializable]
    public struct FVector3
    {
        public Fix64 x;
        public Fix64 y;
        public Fix64 z;


        public FVector3(Fix64 xyz)
        {
            x = xyz;
            y = xyz;
            z = xyz;
        }

        public FVector3(Fix64 x, Fix64 y)
        {
            this.x = x;
            this.y = y;
            this.z = Fix64.zero;
        }

        public FVector3(Fix64 x, Fix64 y, Fix64 z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        public static FVector3 back { get { return _back; } }
        static FVector3 _back = new FVector3(Fix64.zero, Fix64.zero, -Fix64.one);

        public static FVector3 forward { get { return _forward; } }
        static FVector3 _forward = new FVector3(Fix64.zero, Fix64.zero, Fix64.one);

        public static FVector3 down { get { return _down; } }
        static FVector3 _down = new FVector3(Fix64.zero, -Fix64.one, Fix64.zero);

        public static FVector3 up { get { return _up; } }
        static FVector3 _up = new FVector3(Fix64.zero, Fix64.one, Fix64.zero);

        public static FVector3 left { get { return _left; } }
        static FVector3 _left = new FVector3(-Fix64.one, Fix64.zero, Fix64.zero);

        public static FVector3 right { get { return _right; } }
        static FVector3 _right = new FVector3(Fix64.one, Fix64.zero, Fix64.zero);

        public static FVector3 zero { get { return _zero; } }
        static FVector3 _zero = new FVector3(Fix64.zero, Fix64.zero, Fix64.zero);

        internal static FVector3 InternalZero = _zero;

        public static FVector3 one { get { return _one; } }
        static FVector3 _one = new FVector3(Fix64.one, Fix64.one, Fix64.one);


        public static readonly Fix64 kEpsilon = Fix64.FromSingle(1E-05f);
        public static readonly Fix64 kEpsilonNormalSqrt = Fix64.FromSingle(1E-15f);

        private static Fix64 ZeroEpsilonSq = Fix64.EN3;

        /// <summary>
        /// length of vector.
        /// </summary>
        public Fix64 magnitude
        {
            get
            {
                return (x * x + y * y + z * z).Sqrt();
            }
        }

        /// <summary>
        /// squared of length.
        /// </summary>
        public Fix64 sqrMagnitude
        {
            get
            {
                return x * x + y * y + z * z;
            }
        }

        public FVector3 normalized
        {
            get
            {
                Fix64 len = magnitude;

                if (len == Fix64.zero)
                {
                    return FVector3.zero;
                }

                return new FVector3(x / len, y / len, z / len);
            }
        }

        public override string ToString()
        {
            return x.ToString() + "," + y.ToString() + "," + z.ToString();
        }

        public override bool Equals(object obj)
        {
            return ((FVector3)obj).x == x &&
                   ((FVector3)obj).y == y &&
                   ((FVector3)obj).z == z;
        }

        public override int GetHashCode()
        {
            return magnitude.GetHashCode();
        }

        public static void Normalize(ref FVector3 value, out FVector3 result)
        {
            Fix64 len = value.magnitude;
            if (len <= Fix64.EN6) //不要去除以0
            {
                result = FVector3.zero;
                return;
            }
            result.x = value.x / len;
            result.y = value.y / len;
            result.z = value.z / len;
        }

        public void Normalize()
        {
            Fix64 len = magnitude;
            if(len <= Fix64.EN6) //不要去除以0
            {
                x = 0;
                y = 0;
                z = 0;
                return;
            }
            x /= len;
            y /= len;
            z /= len;
        }

        public void Scale(FVector3 scale)
        {
            x *= scale.x;
            y *= scale.y;
            z *= scale.z;
        }

        public void Set(Fix64 x, Fix64 y, Fix64 z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        public void MakeZero()
        {
            x = Fix64.zero;
            y = Fix64.zero;
            z = Fix64.zero;
        }

        public bool IsNearlyZero()
        {
            return (sqrMagnitude < ZeroEpsilonSq);
        }

        public static void Swap(ref FVector3 vector1, ref FVector3 vector2)
        {
            Fix64 temp;

            temp = vector1.x;
            vector1.x = vector2.x;
            vector2.x = temp;

            temp = vector1.y;
            vector1.y = vector2.y;
            vector2.y = temp;

            temp = vector1.z;
            vector1.z = vector2.z;
            vector2.z = temp;
        }

        public static FVector3 operator +(FVector3 a, FVector3 b)
        {
            return new FVector3(a.x + b.x, a.y + b.y, a.z + b.z);
        }

        public static FVector3 operator -(FVector3 a)
        {
            return new FVector3(-a.x, -a.y, -a.z);
        }

        public static FVector3 operator -(FVector3 a, FVector3 b)
        {
            return new FVector3(a.x - b.x, a.y - b.y, a.z - b.z);
        }

        public static FVector3 operator *(Fix64 d, FVector3 a)
        {
            return new FVector3(a.x * d, a.y * d, a.z * d);
        }

        public static FVector3 operator *(FVector3 a, Fix64 d)
        {
            return new FVector3(a.x * d, a.y * d, a.z * d);
        }

        public static Fix64 operator *(FVector3 a, FVector3 b)
        {
            return a.x * b.x + a.y * b.y + a.z * b.z;
        }

        public static FVector3 operator /(FVector3 a, Fix64 d)
        {
            return new FVector3(a.x / d, a.y / d, a.z / d);
        }

        public static bool operator ==(FVector3 lhs, FVector3 rhs)
        {
            return lhs.x == rhs.x && lhs.y == rhs.y && lhs.z == rhs.z;
        }

        public static bool operator !=(FVector3 lhs, FVector3 rhs)
        {
            return lhs.x != rhs.x || lhs.y != rhs.y || lhs.z != rhs.z;
        }

        public void ClampMagnitude(Fix64 maxLength)
        {
            Fix64 len = magnitude;

            if (len > maxLength)
            {
                // x / magnitude * maxLength, y / magnitude * maxLength
                // x * (maxLength / magnitude), y * (maxLength / magnitude)
                //Fix64 scaleFactor = Fix64.Div(maxLength, len);
                Fix64 invScaleFactor = len / maxLength;

                //x = Fix64.Mul(x, scaleFactor);
                //y = Fix64.Mul(y, scaleFactor);
                //z = Fix64.Mul(z, scaleFactor);

                x /= invScaleFactor;
                y /= invScaleFactor;
                z /= invScaleFactor;
            }
        }

        /// <summary>
        /// 求两个向量的欧几里得距离.
        /// </summary>
        /// <returns></returns>
        public static Fix64 Distance(FVector3 a, FVector3 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;
            Fix64 dz = a.z - b.z;

            //return Fix64.Sqrt(Fix64.Square(dx) + Fix64.Square(dy) + Fix64.Square(dz));
            return (dx * dx + dy * dy + dz * dz).Sqrt();
        }

        /// <summary>
        /// 求两个向量的欧几里得距离的平方.
        /// </summary>
        /// <returns></returns>
        public Fix64 SqrDistance(FVector3 a, FVector3 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;
            Fix64 dz = a.z - b.z;

            //return Fix64.Square(dx) + Fix64.Square(dy) + Fix64.Square(dz);
            return dx * dx + dy * dy + dz * dz;
        }

        /// <summary>
        /// Inverses the direction of a vector.
        /// </summary>
        /// <param name="value">The vector to inverse.</param>
        /// <returns>The negated vector.</returns>
        public static FVector3 Negate(FVector3 value)
        {
            FVector3 result;
            FVector3.Negate(ref value, out result);
            return result;
        }

        /// <summary>
        /// Inverses the direction of a vector.
        /// </summary>
        /// <param name="value">The vector to inverse.</param>
        /// <param name="result">The negated vector.</param>
        public static void Negate(ref FVector3 value, out FVector3 result)
        {
            result.x = -value.x;
            result.y = -value.y;
            result.z = -value.z;
        }

        public void Negate()
        {
            x = -x;
            y = -y;
            z = -z;
        }

        /// <summary>
        /// Transforms a vector by the given matrix.
        /// </summary>
        /// <param name="position">The vector to transform.</param>
        /// <param name="matrix">The transform matrix.</param>
        /// <param name="result">The transformed vector.</param>
        ///   v * M
        public static void Transform(ref FVector3 position, ref FMatrix3x3 matrix, out FVector3 result)
        {
            result.x = ((position.x * matrix.m00) + (position.y * matrix.m10)) + (position.z * matrix.m20);
            result.y = ((position.x * matrix.m01) + (position.y * matrix.m11)) + (position.z * matrix.m21);
            result.z = ((position.x * matrix.m02) + (position.y * matrix.m12)) + (position.z * matrix.m22);
        }

        public static FVector3 Transform(FVector3 position, FMatrix3x3 matrix)
        {
            FVector3 result;
            Transform(ref position, ref matrix, out result);
            return result;
        }


        /// <summary>
        /// Transforms a vector by the transposed of the given Matrix.
        /// </summary>
        /// <param name="position">The vector to transform.</param>
        /// <param name="matrix">The transform matrix.</param>
        /// <param name="result">The transformed vector.</param>
        /// v * M^t
        public static void TransposedTransform(ref FVector3 position, ref FMatrix3x3 matrix, out FVector3 result)
        {
            result.x = ((position.x * matrix.m00) + (position.y * matrix.m01)) + (position.z * matrix.m02);
            result.y = ((position.x * matrix.m10) + (position.y * matrix.m11)) + (position.z * matrix.m12);
            result.z = ((position.x * matrix.m20) + (position.y * matrix.m21)) + (position.z * matrix.m22);
        }

        /// <summary>
        /// Subtracts to vectors.
        /// </summary>
        /// <param name="value1">The first vector.</param>
        /// <param name="value2">The second vector.</param>
        /// <param name="result">The difference of both vectors.</param>
        public static void Subtract(ref FVector3 value1, ref FVector3 value2, out FVector3 result)
        {
            result.x = value1.x - value2.x;
            result.y = value1.y - value2.y;
            result.z = value1.z - value2.z;
        }

        /// <summary>
        /// Multiply a vector with a factor.
        /// </summary>
        /// <param name="value1">The vector to multiply.</param>
        /// <param name="scaleFactor">The scale factor.</param>
        /// <param name="result">Returns the multiplied vector.</param>
        public static void Multiply(ref FVector3 value1, Fix64 scaleFactor, out FVector3 result)
        {
            result.x = value1.x * scaleFactor;
            result.y = value1.y * scaleFactor;
            result.z = value1.z * scaleFactor;
        }

        /// <summary>
        /// Adds to vectors.
        /// </summary>
        /// <param name="value1">The first vector.</param>
        /// <param name="value2">The second vector.</param>
        /// <param name="result">The sum of both vectors.</param>
        public static void Add(ref FVector3 value1, ref FVector3 value2, out FVector3 result)
        {
            result.x = value1.x + value2.x;
            result.y = value1.y + value2.y;
            result.z = value1.z + value2.z;
        }

        public static Fix64 Dot(ref FVector3 a, ref FVector3 b)
        {
            return a.x * b.x + a.y * b.y + a.z * b.z;
        }

        public static Fix64 Dot(FVector3 a, FVector3 b)
        {
            return a.x * b.x + a.y * b.y + a.z * b.z;
        }

        /// <summary>
        /// ax bx
        /// ay by
        /// az bz
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        public static FVector3 Cross(FVector3 a, FVector3 b)
        {
            FVector3 result;
            Cross(ref a, ref b, out result);
            return result;
        }

        public static void Cross(ref FVector3 a, ref FVector3 b, out FVector3 result)
        {
            result.x = a.y * b.z - a.z * b.y;
            result.y = a.z * b.x - a.x * b.z;
            result.z = a.x * b.y - a.y * b.x;
        }

        public static FVector3 Scale(FVector3 a, FVector3 b)
        {
            //return new FVector3(Fix64.Mul(a.x, b.x), Fix64.Mul(a.y, b.y), Fix64.Mul(a.z, b.z));
            return new FVector3(a.x * b.x, a.y * b.y, a.z * b.z);
        }

        public static FVector3 Lerp(FVector3 a, FVector3 b, Fix64 t)
        {
            return new FVector3(
                Fix64.Lerp(a.x, b.x, t),
                Fix64.Lerp(a.y, b.y, t),
                Fix64.Lerp(a.z, b.z, t));
        }

        public static FVector3 LerpClamped(FVector3 a, FVector3 b, Fix64 t)
        {
            if (t > Fix64.one)
            {
                t = Fix64.one;
            }
            else if (t < Fix64.zero)
            {
                t = Fix64.zero;
            }

            return Lerp(a, b, t);
        }

        public static FVector3 Max(FVector3 a, FVector3 b)
        {
            FVector3 result;
            Max(ref a, ref b, out result);
            return result;
        }


        public static void Max(ref FVector3 a, ref FVector3 b, out FVector3 result)
        {
            result.x = Fix64.Max(a.x, b.x);
            result.y = Fix64.Max(a.y, b.y);
            result.z = Fix64.Max(a.z, b.z);
        }

        public static FVector3 Min(FVector3 a, FVector3 b)
        {
            FVector3 result;
            Min(ref a, ref b, out result);
            return result;
        }

        public static void Min(ref FVector3 a, ref FVector3 b, out FVector3 result)
        {
            result.x = Fix64.Min(a.x, b.x);
            result.y = Fix64.Min(a.y, b.y);
            result.z = Fix64.Min(a.z, b.z);
        }

        /// <summary>
        /// 求两个向量的夹角.(依赖于反三角函数的实现)
        /// </summary>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <returns></returns>
        public static Fix64 Angle(FVector3 from, FVector3 to)
        {
            Fix64 dot = Dot(from, to);
            Fix64 len2 = from.magnitude * to.magnitude;

            return Fix64.Acos(dot / len2);
        }


        /// <summary>
        /// 求反射向量.
        /// </summary>
        /// <param name="inDirection">单位向量</param>
        /// <param name="inNormal">单位向量</param>
        /// <returns></returns>   
        public static FVector3 Reflect(FVector3 inDirection, FVector3 inNormal)
        {
            Fix64 projectionLen2 = Dot(inDirection, inNormal) * 2;
            FVector3 v = inNormal * -projectionLen2;

            return inDirection + v;
        }

        /// <summary>
        /// 向量v在n上的投影.
        /// </summary>
        /// <param name="v"></param>
        /// <param name="n">n为单位向量</param>
        /// <returns></returns>
        public static FVector3 Project(FVector3 v, FVector3 n)
        {
            return n * Dot(v, n);
        }

        public static FVector3 ProjectOnPlane(FVector3 v, FVector3 n)
        {
            return n * Dot(v, n) + v;
        }

        public static FVector3 Slerp(FVector3 a, FVector3 b, Fix64 t)
        {
            Fix64 lenA = a.magnitude;
            Fix64 lenB = b.magnitude;

            FVector3 v1 = a.normalized;
            FVector3 v2 = b.normalized;

            Fix64 cos_theta = Dot(v1, v2);

            if ((Fix64.one - cos_theta) < Fix64.epsilon && (Fix64.one - cos_theta) > -Fix64.epsilon)
            {
                return a + (b - a) * t;
            }

            Fix64 angle = Fix64.Acos(cos_theta);
            FVector3 axis = Cross(a, b);

            FQuaternion q = FQuaternion.AngleAxis(angle * t, axis);
            FVector3 vt = q * v1;

            return (lenA + (lenB - lenA) * t) * vt;
        }
    }
}
