﻿
using System.Collections.Generic;
using Common.Pool;
using GCloud.LockStep;
using static Common.FrameDriving.FSJitterBufferedGenerator;

namespace Common.FrameDriving
{
    /// <summary>
    /// 每次调用pop，如果当前队列的帧数量大于CachedFrameCount，
    /// 则一次性把多余的帧触发出去（触发的策略暂时为一次性触发，后续考虑加入一个平滑的加减速方式）
    /// 如果当前队列的帧数量小于或等于如果当前队列的帧数量大于CachedFrameCount，则记录上一帧的pop时间，
    /// 取得当前时间，算得时间差DT，通过DT来覆盖所需要触发的cachedFrame。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class FSJitterBuffer<T>
    {
        protected class FSJitterBufferCalculator
        {
            /// <summary>
            /// 帧间隔，等于同步帧的时间间隔.
            /// </summary>
            public int Interval { get; set; }
            public CalSpeed CalSpeedDelegate = null;
            int elapsedFromLastFrame;
            int lastFrameId;
            int latestFrameId;
            int frequency = 1;
            public int Frequency
            {
                get
                {
                    return frequency;
                }
                set
                {
                    if (value > 0)
                    {
                        frequency = value;
                    }
                }
            }

            public int LatestFrameId
            {
                get
                {
                    return latestFrameId;
                }
                set
                {
                    latestFrameId = value;
                }
            }
            public int LastFrameId
            {
                get
                {
                    return lastFrameId;
                }
                set
                {
                    if (lastFrameId < value)
                    {
                        elapsedFromLastFrame -= Interval * (value - lastFrameId) / Frequency;
                        lastFrameId = value;
                        if (lastFrameId == LatestFrameId || elapsedFromLastFrame < 0)
                        {
                            elapsedFromLastFrame = 0;
                        }
                    }
                }
            }
            public int LastFrameTick
            {
                get
                {
                    return LastFrameId * Interval / Frequency;
                }
            }
            public int LatestFrameTick
            {
                get
                {
                    return LatestFrameId * Interval / Frequency;
                }
            }
            public void AddElapsedTime(int elapsedTime)
            {
                elapsedFromLastFrame = elapsedTime;
            }
            public int CalcCurPopTick()
            {
                int currentMaxFrameId = (LockSteper.Instance as LockStepImpl).GetCurrentMaxFrameId();
                double speed = 0;
                if (CalSpeedDelegate != null)  //策略
                {
                    speed = CalSpeedDelegate(currentMaxFrameId, LastFrameId);
                }
                else
                {
                    speed = System.Math.Sqrt((float)((LatestFrameId - LastFrameId) / Frequency));
                    if (speed < 1)
                    {
                        speed = 1.0f;
                    }
                    else if (speed > 8)
                    {
                        speed = 8.0f;
                    }
                }

                int tick = (int)(LastFrameTick + elapsedFromLastFrame * speed);
                return tick;
            }
        }
        protected class ItemPoolable : FS_PoolableObject
        {
            public int pushTime;
            public int frameId;
            public bool keyFrame;
            public T data;

            public ItemPoolable()
            {
            }

            public override void OnReuse() { }

            protected override void ResetAllFields() 
            {
                pushTime = 0;
                frameId = 0;
                keyFrame = false;
                data = default(T);
            }


            protected override void OnRelease()
            {
                ResetAllFields();
            }
        }

        protected FSJitterBufferCalculator _calc = new FSJitterBufferCalculator();
        public CalSpeed CalSpeedDelegate
        {
            get
            {
                return _calc.CalSpeedDelegate;
            }
            set
            {
                _calc.CalSpeedDelegate = value;
            }
        }

        public delegate void OnPop(T data);

        public FSJitterBuffer(int frameInterval)
        {
            this.frameInterval = frameInterval;
            _calc.Interval = frameInterval;
        }

        public virtual void Push(int frameId, T data)
        {
            ItemPoolable item = FS_ClassPool<ItemPoolable>.Get();
            item.pushTime = frameId * frameInterval;
            item.frameId = frameId;
            item.keyFrame = true;
            item.data = data;

            mDataQueue.Enqueue(item);
            _calc.LatestFrameId = frameId;
        }

        /// <summary>
        /// 帧间隔，等于同步帧的时间间隔.
        /// </summary>
        public int frameInterval { get; set; }

        public int popedFrameCount { get; protected set; }

        public void Pop(int elapsedTime, OnPop fun)
        {
            _calc.AddElapsedTime(elapsedTime);
            if (Count > 0)
            {
                int popTick = _calc.CalcCurPopTick();
                ItemPoolable item = mDataQueue.Peek();
                bool keyFramePoped = false;
                // 如果当前帧是关键帧，则而且还没有pop出来关键帧，则一定要把它pop出来，降低延时
                // 如果已经pop出来关键帧了，则按快播时间，取帧
                // 因为非关键帧，依赖渲染帧的间隔，所以
                //      2倍频的话，最差渲染帧>=15帧
                //      3倍步的话，最差渲染帧>=30帧
                while(item.pushTime <= popTick || (!keyFramePoped && item.keyFrame))
                //while (Count > 0)
                {
                    if (item.keyFrame)
                    {
                        keyFramePoped = true;
                    }
                    fun(item.data);

                    mDataQueue.Dequeue();
                    _calc.LastFrameId = item.frameId;
                    item.Release();
                    popedFrameCount++;
                    if (Count > 0)
                    {
                        item = mDataQueue.Peek();
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }

        public int Count
        {
            get { return mDataQueue.Count; }
        }

        public void Clear()
        {
            popedFrameCount = 0;
            mDataQueue.Clear();
        }



        protected Queue<ItemPoolable> mDataQueue = new Queue<ItemPoolable>();
    }

    public class FSJitterBufferFraequency : FSJitterBuffer<FrameInfoPoolable>
    {
        public FSJitterBufferFraequency(int interval, int frequency) : base(interval)
        {
            _calc.Frequency = frequency;   
        }

        public override void Push(int frameId, FrameInfoPoolable data)
        {
            int everySpan = data.TimeSpan / _calc.Frequency;
            int count = _calc.Frequency - 1;
            int tempFrameId = frameId * _calc.Frequency;

            data.TimeSpan = everySpan;
            ItemPoolable item = FS_ClassPool<ItemPoolable>.Get();
            item.pushTime = frameId * frameInterval;
            item.frameId = tempFrameId++;
            item.keyFrame = true;
            item.data = data;

            mDataQueue.Enqueue(item);
            _calc.LatestFrameId = item.frameId;

            while(count>0)
            {
                count--;
                FrameInfoPoolable frameInfoPoolable = FS_ClassPool<FrameInfoPoolable>.Get();
                frameInfoPoolable.TimeSpan = everySpan;
                frameInfoPoolable.frameInfo.FrameId = tempFrameId++;
                ItemPoolable itemAdd = FS_ClassPool<ItemPoolable>.Get();
                itemAdd.pushTime = frameId * frameInterval + everySpan;
                itemAdd.frameId = frameInfoPoolable.frameInfo.FrameId;
                itemAdd.data = frameInfoPoolable;

                mDataQueue.Enqueue(itemAdd);
                _calc.LatestFrameId = itemAdd.frameId;
            }
        }
    }
}
