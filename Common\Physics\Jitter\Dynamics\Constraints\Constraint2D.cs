﻿using FixedMath;

namespace Common.Physics {

	public class Constraint2D : Constraint {

        private bool freezeZAxis;

		public Constraint2D(RigidBody body, bool freezeZAxis) : base(body, null) {
            this.freezeZAxis = freezeZAxis;

        }			

		public override void PostStep() {
			FVector3 pos = Body1.Position;
			pos.z = 0;
			Body1.Position = pos;

			FQuaternion q = FQuaternion.MatrixToQuaternion(Body1.Orientation);
			q.Normalize();
			q.x = 0;
			q.y = 0;

			if (freezeZAxis) {
				q.z = 0;
			}

			Body1.Orientation = FMatrix3x3.CreateFromQuaternion(q);

			if (Body1.isStatic) {
				return;
			}

			FVector3 vel = Body1.LinearVelocity;
			vel.z = 0;
			Body1.LinearVelocity = vel;

            FVector3 av = Body1.AngularVelocity;
			av.x = 0;
			av.y = 0;

            if (freezeZAxis) {
                av.z = 0;
            }

			Body1.AngularVelocity = av;
		}

	}

}