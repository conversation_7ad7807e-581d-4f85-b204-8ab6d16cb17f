﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

using FixedMath;

namespace Common.Physics {

    #region Constraint Equations
    // Constraint formulation:
    // 
    // C_1 = R1_x - R2_x
    // C_2 = ...
    // C_3 = ...
    //
    // Derivative:
    //
    // dC_1/dt = w1_x - w2_x
    // dC_2/dt = ...
    // dC_3/dt = ...
    //
    // Jacobian:
    // 
    // dC/dt = J*v+b
    //
    // v = (v1x v1y v1z w1x w1y w1z v2x v2y v2z w2x w2y w2z)^(T) 
    //
    //     v1x v1y v1z w1x w1y w1z v2x v2y v2z w2x w2y w2z
    //     -------------------------------------------------
    // J = 0   0   0   1   0    0   0   0   0   -1   0   0   <- dC_1/dt
    //     0   0   0   0   1    0   0   0   0    0  -1   0   <- ...  
    //     0   0   0   0   0    1   0   0   0    0   0  -1   <- ...
    //
    // Effective Mass:
    //
    // 1/m_eff = [J^T * M^-1 * J] = I1^(-1) + I2^(-1)
    #endregion

    /// <summary>
    /// The AngleConstraint constraints two bodies to always have the same relative
    /// orientation to each other. Combine the AngleConstraint with a PointOnLine
    /// Constraint to get a prismatic joint.
    /// </summary>
    public class FixedAngle : Constraint
    {

        private Fix64 biasFactor = 5 * Fix64.EN2;
        private Fix64 softness = Fix64.zero;

        private FVector3 accumulatedImpulse;

        private FMatrix3x3 initialOrientation1, initialOrientation2;

        /// <summary>
        /// Constraints two bodies to always have the same relative
        /// orientation to each other. Combine the AngleConstraint with a PointOnLine
        /// Constraint to get a prismatic joint.
        /// </summary>
        public FixedAngle(RigidBody body1, RigidBody body2) : base(body1, body2)
        {
            initialOrientation1 = body1.orientation;
            initialOrientation2 = body2.orientation;

            //orientationDifference = body1.orientation * body2.invOrientation;
            //orientationDifference = JMatrix.Transpose(orientationDifference);
        }

        public FVector3 AppliedImpulse { get { return accumulatedImpulse; } }

        public FMatrix3x3 InitialOrientationBody1 { get { return initialOrientation1; } set { initialOrientation1 = value; } }
        public FMatrix3x3 InitialOrientationBody2 { get { return initialOrientation2; } set { initialOrientation2 = value; } }

        /// <summary>
        /// Defines how big the applied impulses can get.
        /// </summary>
        public Fix64 Softness { get { return softness; } set { softness = value; } }

        /// <summary>
        /// Defines how big the applied impulses can get which correct errors.
        /// </summary>
        public Fix64 BiasFactor { get { return biasFactor; } set { biasFactor = value; } }

        FMatrix3x3 effectiveMass;
        FVector3 bias;
        Fix64 softnessOverDt;
        
        /// <summary>
        /// Called once before iteration starts.
        /// </summary>
        /// <param name="timestep">The 5simulation timestep</param>
        public override void PrepareForIteration(Fix64 timestep)
        {
            effectiveMass = body1.invInertiaWorld + body2.invInertiaWorld;

            softnessOverDt = softness / timestep;

            effectiveMass.m00 += softnessOverDt;
            effectiveMass.m11 += softnessOverDt;
            effectiveMass.m22 += softnessOverDt;

            FMatrix3x3.Invert(ref effectiveMass, out effectiveMass);

            FMatrix3x3 orientationDifference;
            FMatrix3x3.Multiply(ref initialOrientation1, ref initialOrientation2, out orientationDifference);
            FMatrix3x3.Transpose(ref orientationDifference, out orientationDifference);

            FMatrix3x3 q = orientationDifference * body2.invOrientation * body1.orientation;
            FVector3 axis;

            Fix64 x = q.m21 - q.m12;
            Fix64 y = q.m02 - q.m20;
            Fix64 z = q.m10 - q.m01;

            Fix64 r = Fix64.Sqrt(x * x + y * y + z * z);
            Fix64 t = q.m00 + q.m11 + q.m22;

            Fix64 angle = Fix64.Atan2(r, t - 1);
            axis = new FVector3(x, y, z) * angle;

            if (r != Fix64.zero) axis = axis * (Fix64.one / r);

            bias = axis * biasFactor * (-Fix64.one / timestep);

            // Apply previous frame solution as initial guess for satisfying the constraint.
            if (!body1.IsStatic) body1.angularVelocity += FVector3.Transform(accumulatedImpulse, body1.invInertiaWorld);
            if (!body2.IsStatic) body2.angularVelocity += FVector3.Transform(-Fix64.one * accumulatedImpulse, body2.invInertiaWorld);
        }

        /// <summary>
        /// Iteratively solve this constraint.
        /// </summary>
        public override void Iterate()
        {
            FVector3 jv = body1.angularVelocity - body2.angularVelocity;

            FVector3 softnessVector = accumulatedImpulse * softnessOverDt;

            FVector3 lambda = -Fix64.one * FVector3.Transform(jv+bias+softnessVector, effectiveMass);

            accumulatedImpulse += lambda;

            if(!body1.IsStatic) body1.angularVelocity += FVector3.Transform(lambda, body1.invInertiaWorld);
            if(!body2.IsStatic) body2.angularVelocity += FVector3.Transform(-Fix64.one * lambda, body2.invInertiaWorld);
        }

    }
}
