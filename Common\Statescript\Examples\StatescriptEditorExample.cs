#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;
using GP.Common.Statescript.Editor;

namespace GP.Common.Statescript.Examples
{
    /// <summary>
    /// Example script for creating and testing Statescript graphs in the Unity Editor
    /// </summary>
    public static class StatescriptEditorExample
    {
        [MenuItem("Statescript/Examples/Create Simple Example Graph")]
        public static void CreateSimpleExampleGraph()
        {
            // Create a new Statescript asset
            var asset = ScriptableObject.CreateInstance<StatescriptAsset>();
            
            // Create the graph
            var graph = new StatescriptGraph("Simple Example");
            graph.Description = "A simple example graph created from code";

            // Create nodes
            var entryNode = new EntryNode();
            entryNode.Id = 1;
            entryNode.Name = "Entry";

            var logNode1 = new LogNode();
            logNode1.Id = 2;
            logNode1.Name = "Log Start";
            logNode1.SetProperty("Message", "Starting example sequence...");

            var waitNode = new WaitNode();
            waitNode.Id = 3;
            waitNode.Name = "Wait 2 Seconds";
            waitNode.SetProperty("Duration", 2.0f);

            var logNode2 = new LogNode();
            logNode2.Id = 4;
            logNode2.Name = "Log End";
            logNode2.SetProperty("Message", "Example sequence completed!");

            // Add nodes to graph
            graph.AddNode(entryNode);
            graph.AddNode(logNode1);
            graph.AddNode(waitNode);
            graph.AddNode(logNode2);

            // Create connections
            var connection1 = new StatescriptConnection(1, 2);
            connection1.Id = 1;
            var connection2 = new StatescriptConnection(2, 3);
            connection2.Id = 2;
            var connection3 = new StatescriptConnection(3, 4);
            connection3.Id = 3;

            graph.AddConnection(connection1);
            graph.AddConnection(connection2);
            graph.AddConnection(connection3);

            // Create editor data for visual layout
            var editorData = asset.EditorData;
            
            // Position nodes in a nice layout
            editorData.SetNodeData(1, new NodeEditorData
            {
                NodeId = 1,
                Position = new Vector2(100, 200),
                Size = new Vector2(100, 50),
                Color = new Color(0.2f, 0.8f, 0.2f, 1.0f) // Green for entry
            });
            
            editorData.SetNodeData(2, new NodeEditorData
            {
                NodeId = 2,
                Position = new Vector2(250, 200),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f) // Blue for action
            });
            
            editorData.SetNodeData(3, new NodeEditorData
            {
                NodeId = 3,
                Position = new Vector2(400, 200),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f) // Blue for action
            });
            
            editorData.SetNodeData(4, new NodeEditorData
            {
                NodeId = 4,
                Position = new Vector2(550, 200),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f) // Blue for action
            });

            // Save the graph to the asset
            asset.SaveGraph(graph);

            // Save the asset to disk
            var path = "Assets/SimpleStatescriptExample.asset";
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Select the asset and open the graph editor
            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);
            
            Debug.Log($"Created simple Statescript example at: {path}");
            Debug.Log("Double-click the asset to open it in the Graph Editor!");
        }

        [MenuItem("Statescript/Examples/Create Branching Example Graph")]
        public static void CreateBranchingExampleGraph()
        {
            var asset = ScriptableObject.CreateInstance<StatescriptAsset>();
            
            var graph = new StatescriptGraph("Branching Example");
            graph.Description = "Example with conditional branching";

            // Add a variable
            var healthVariable = new StatescriptVariable("Health", StatescriptVariableType.Integer, 75);
            graph.Variables.Add(healthVariable);

            // Create nodes
            var entryNode = new EntryNode { Id = 1, Name = "Entry" };
            
            var conditionNode = new ConditionNode
            {
                Id = 2,
                Name = "Check Health",
                Type = ConditionType.Variable,
                LeftOperand = "Health",
                RightOperand = "50",
                Operator = ComparisonOperator.Greater
            };

            var highHealthLog = new LogNode { Id = 3, Name = "High Health" };
            highHealthLog.SetProperty("Message", "Health is high!");

            var lowHealthLog = new LogNode { Id = 4, Name = "Low Health" };
            lowHealthLog.SetProperty("Message", "Health is low!");

            // Add nodes
            graph.AddNode(entryNode);
            graph.AddNode(conditionNode);
            graph.AddNode(highHealthLog);
            graph.AddNode(lowHealthLog);

            // Create connections
            graph.AddConnection(new StatescriptConnection(1, 2) { Id = 1 });

            var trueConnection = new StatescriptConnection(2, 3) { Id = 2 };
            trueConnection.SetProperty("Type", "True");
            
            var falseConnection = new StatescriptConnection(2, 4) { Id = 3 };
            falseConnection.SetProperty("Type", "False");

            graph.AddConnection(trueConnection);
            graph.AddConnection(falseConnection);

            // Create editor layout
            var editorData = asset.EditorData;
            
            editorData.SetNodeData(1, new NodeEditorData
            {
                NodeId = 1,
                Position = new Vector2(100, 200),
                Size = new Vector2(100, 50),
                Color = new Color(0.2f, 0.8f, 0.2f, 1.0f)
            });
            
            editorData.SetNodeData(2, new NodeEditorData
            {
                NodeId = 2,
                Position = new Vector2(300, 200),
                Size = new Vector2(140, 70),
                Color = new Color(1.0f, 0.8f, 0.2f, 1.0f) // Yellow for condition
            });
            
            editorData.SetNodeData(3, new NodeEditorData
            {
                NodeId = 3,
                Position = new Vector2(500, 150),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f)
            });
            
            editorData.SetNodeData(4, new NodeEditorData
            {
                NodeId = 4,
                Position = new Vector2(500, 250),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f)
            });

            // Set connection colors
            editorData.SetConnectionData(2, new ConnectionEditorData
            {
                ConnectionId = 2,
                Color = Color.green,
                Width = 2f,
                Label = "True",
                ShowLabel = true
            });
            
            editorData.SetConnectionData(3, new ConnectionEditorData
            {
                ConnectionId = 3,
                Color = Color.red,
                Width = 2f,
                Label = "False",
                ShowLabel = true
            });

            asset.SaveGraph(graph);

            var path = "Assets/BranchingStatescriptExample.asset";
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);
            
            Debug.Log($"Created branching Statescript example at: {path}");
        }

        [MenuItem("Statescript/Examples/Open Graph Editor")]
        public static void OpenGraphEditor()
        {
            StatescriptGraphEditor.OpenWindow();
        }

        [MenuItem("Statescript/Examples/Test Serialization Performance")]
        public static void TestSerializationPerformance()
        {
            var graph = CreateComplexTestGraph();
            
            var stats = GP.Common.Statescript.Serialization.StatescriptSerializer.GetSerializationStats(graph);
            
            Debug.Log("=== Serialization Performance Test ===");
            Debug.Log($"Graph: {graph.Nodes.Count} nodes, {graph.Connections.Count} connections");
            Debug.Log($"JSON Size: {stats.JsonSize} bytes");
            Debug.Log($"Binary Size: {stats.BinarySize} bytes");
            Debug.Log($"Compressed Size: {stats.CompressedBinarySize} bytes");
            Debug.Log($"Binary vs JSON: {((float)stats.BinarySize / stats.JsonSize):P1} size");
            Debug.Log($"Compression Ratio: {stats.CompressionRatio:F2}");
        }

        private static StatescriptGraph CreateComplexTestGraph()
        {
            var graph = new StatescriptGraph("Complex Test Graph");
            
            // Create many nodes for performance testing
            for (int i = 0; i < 50; i++)
            {
                var node = new LogNode();
                node.Id = i + 1;
                node.Name = $"Log Node {i + 1}";
                node.SetProperty("Message", $"This is log message number {i + 1}");
                graph.AddNode(node);
                
                if (i > 0)
                {
                    var connection = new StatescriptConnection(i, i + 1);
                    connection.Id = i;
                    graph.AddConnection(connection);
                }
            }
            
            // Add some variables
            for (int i = 0; i < 10; i++)
            {
                var variable = new StatescriptVariable($"Variable{i}", StatescriptVariableType.Integer, i * 10);
                graph.Variables.Add(variable);
            }
            
            return graph;
        }
    }
}
#endif
