﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common.FrameDriving
{
    public class FrameGeneratorHelper
    {
        /// <summary>
        ///  生成一帧。
        /// </summary>
        /// <param name="frameDriver"></param>
        /// <param name="duration"></param>
        public void GenerateOneFrame(FrameDriver frameDriver, int elapsedTime, object data)
        {
            Frame frame = AllocFrame(frameDriver, elapsedTime, data);

            if (null != frame)
            {
                frameDriver.frameQueue.Enqueue(frame);
            }
        }

        /// <summary>
        ///  生成一帧。
        /// </summary>
        /// <param name="frameDriver"></param>
        /// <param name="duration"></param>
        public Frame AllocFrame(FrameDriver frameDriver, int elapsedTime, object data)
        {
            Frame frame = frameDriver.framePool.Alloc();

            if (null != frame)
            {
                frame.seqNum = nextFrameId++;
                frame.timeSpan = elapsedTime;
                frame.data = data;
            }

            return frame;
        }


        //计算过程中的状态变量
        public uint nextFrameId { get; set; }
    }
}
