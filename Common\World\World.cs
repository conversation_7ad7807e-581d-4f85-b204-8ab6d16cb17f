using System;
using System.Collections.Generic;

namespace GP.Common
{
    public enum eWorldType : byte {
        Logic, Visual
    }

    public enum eWorldStatus : byte
    {
        None, Initializing, Ready, Running, Paused, Stopped, Cleaning
    }

    public abstract class World
    {
        public eWorldType WorldType { get; private set; }
        public eWorldStatus WorldStatus { get; private set; }

        public IWorldInitializeData InitializeData { get; private set; }
        protected Dictionary<Type, IManager> m_Managers { get; private set; }
        protected List<IManager> m_UpdateManagers { get; private set; }

        public World(eWorldType worldType)
        {
            WorldType = worldType;
            m_Managers = new Dictionary<Type, IManager>();
            m_UpdateManagers = new List<IManager>();
        }

        public virtual void Initialize(IWorldInitializeData initializeData)
        {
            InitializeData = initializeData;
            WorldStatus = eWorldStatus.Initializing;
        }

        protected T CreateManager<T>(bool requireUpdate = false) where T : IManager, new()
        {
            var manager = new T();
            manager.OnInitialize(this);
            m_Managers[typeof(T)] = manager;
            if (requireUpdate)
            {
                m_UpdateManagers.Add(manager);
            }
            return manager;
        }

        protected bool DestroyManager<T>() where T : IManager
        {
            if (m_Managers.Remove(typeof(T), out var manager))
            {
                manager.OnDestroy();
                m_UpdateManagers.Remove(manager);
                return true;
            }
            return false;
        }

        public T GetManager<T>() where T : IManager
        {
            if (m_Managers.TryGetValue(typeof(T), out var manager))
            {
                return (T)manager;
            }
            return null;
        }

        public void ForEachManager(Action<IManager> action)
        {
            foreach (var manager in m_Managers.Values)
            {
                action(manager);
            }
        }

        public void ForEachManager<P0>(Action<IManager, P0> action, P0 p0)
        {
            foreach (var manager in m_Managers.Values)
            {
                action(manager, p0);
            }
        }

        public void Destroy()
        {
            foreach (var manager in m_Managers.Values)
            {
                manager.OnDestroy();
            }
            m_Managers.Clear();
        }

        public void Tick()
        {
            switch (WorldStatus)
            {
                case eWorldStatus.Initializing:
                    break;
                case eWorldStatus.Running:
                    TickManagers();
                    break;
                case eWorldStatus.Paused:
                    // Do nothing
                    return;
                case eWorldStatus.Stopped:
                case eWorldStatus.Cleaning:
                    return;
                default:
                    throw new InvalidOperationException($"Invalid world status: {WorldStatus}");
            }
        }

        protected void TickManagers()
        {
            if (m_UpdateManagers.Count > 0)
            {
                for (int i = 0; i < m_UpdateManagers.Count; i++)
                {
                    var manager = m_UpdateManagers[i];
                    if (manager != null)
                    {
                        manager.Tick();
                    }
                }
            }
        }
    }
}
