﻿
namespace Common.FrameDriving
{
    /// <summary>
    /// 从lockstep的popframe中收到的
    /// </summary>
    public class FSJitterBufferedGenerator : DefaultFrameGenerator
    {
        public delegate double CalSpeed(int maxFrameId, int currentFrameId);

        public FSJitterBufferedGenerator(FrameDriver frameDriver, int frameInterval)
            : base(frameDriver, frameInterval)
        {
            isJitterBufferEnabled = true;
            JitterBuffer = new FSJitterBuffer<FrameInfoPoolable>(frameInterval);
        }

        public override void OnSyncFrameReceived(FrameInfoPoolable frameInfoPoolable)
        {
            if (isJitterBufferEnabled)
            {
                JitterBuffer.Push(frameInfoPoolable.frameInfo.FrameId, frameInfoPoolable);
            }
            else
            {
                GenerateFrame((FrameInfoPoolable)frameInfoPoolable);
            }
        }
        public override bool IsQueueFull(FrameDriver frameDriver)
        {
            return frameDriver.IsFrameQueueFull(JitterBuffer.Count);
        }
        public override void GenerateFrames(int elapsedTime)
        {
            // push data to filter.
            base.GenerateFrames(elapsedTime);
            ConsumeFrames(elapsedTime);
        }

        //public void ConsumeByJitterBuffer(int elapsedTime)
        public override void ConsumeFrames(int elapsedTime)
        {
            if (isJitterBufferEnabled)
            {
                // generator frame
                JitterBuffer.Pop(elapsedTime, this.GenerateFrame);
            }
        }
        void GenerateFrame(FrameInfoPoolable frameInfoPoolable)
        {
            object frameData = null;

            if (null != ConvertFrameData)
            {
                frameData = ConvertFrameData(frameInfoPoolable.frameInfo);
            }

            // push to jitterbuffer
            GenerateOneFrame(frameDriver, frameInfoPoolable.TimeSpan, frameData);
            frameInfoPoolable.Release();
        }

        public void SetCalSpeed(CalSpeed func)
        {
            JitterBuffer.CalSpeedDelegate = func;
        }

        public FSJitterBuffer<FrameInfoPoolable> JitterBuffer { get; set; }

        public bool isJitterBufferEnabled { get; set; }
    }
}
