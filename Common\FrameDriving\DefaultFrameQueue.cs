﻿
using System.Collections.Generic;

namespace Common.FrameDriving
{
    /// <summary>
    /// 默认的帧队列实现
    /// </summary>
    public class DefaultFrameQueue : IFrameQueue
    {
        /// <summary>
        /// 入队。
        /// 不允许往队列里面加入null。
        /// </summary>
        /// <param name="frame"></param>
        public void Enqueue(Frame frame)
        {
            mFrameQueue.Enqueue(frame);
        }

        /// <summary>
        /// 出队。
        /// </summary>
        /// <returns>返回队列最前的frame，返回空，则表示队列是空的.</returns>
        public Frame Dequeue()
        {
            return mFrameQueue.Dequeue();
        }

        /// <summary>
        /// 取得队列的长度。
        /// </summary>
        public int Count
        {
            get
            {
                return mFrameQueue.Count;
            }
        }

        Queue<Frame> mFrameQueue = new Queue<Frame>();
    }
}
