﻿/*
 * Copyright (c) 2011 <PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

namespace org.critterai.nmgen
{
    /// <summary>
    /// Contour vertex flags. (Applied to the forth element of the vertices arrays.)
    /// </summary>
    /// <remarks>
    /// <para>
    /// Contour vertices take the form (x, y, z, r).  The r-value can contain these flags.
    /// </para>
    /// </remarks>
    [System.Flags]
    public enum ContourFlags
    {
        /// <summary>
        /// The vertex is the start of a border edge.
        /// </summary>
        BorderVertex = 0x10000,

        /// <summary>
        /// The vertex is the start of an edge that forms a boundary between areas.
        /// </summary>
        AreaBorder = 0x20000
    }
}
