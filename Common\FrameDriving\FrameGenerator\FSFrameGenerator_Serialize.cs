﻿
// using System;
// using GCloud.LockStep;
// using UnityEngine;
// using Common.Pool;

// namespace Common.FrameDriving
// {
//     public class FSFrameGenerator_Serialize : IFrameGenerator
//     {
//         public FrameDriver frameDriver 
//         { 
//             get
//             {
//                 return FrameGenerator.frameDriver;
//             }
//         }
//         public DefaultFrameGenerator.ConvertFrameDataDelegate ConvertFrameData
//         {
//             get
//             {
//                 return _CreateFrameDataWrapper;
//             }
//             set 
//             {
//                 FrameGenerator.ConvertFrameData = this.CreateFrameDataWrapper;
//                 _CreateFrameDataWrapper = value;
//             }
//         }
//         public DefaultFrameGenerator.DestroyFrameDataDelegate DestroyFrameData
//         {
//             get
//             {
//                 return FrameGenerator.DestroyFrameData;
//             }
//             set 
//             {
//                 FrameGenerator.DestroyFrameData = value;
//             }
//         }
//         DefaultFrameGenerator.ConvertFrameDataDelegate _CreateFrameDataWrapper;

//         object CreateFrameDataWrapper(FrameInfo frameInfo)
//         {
//             if (null != serialize && null != frameInfo)
//             {
//                 FrameInfoWrapper frameInfoWrapper = new FrameInfoWrapper(frameInfo);
//                 serialize.WriteFrame(frameInfoWrapper);
//             }
//             if (null!=_CreateFrameDataWrapper)
//             {
//                 return _CreateFrameDataWrapper(frameInfo);
//             }
//             return null;
//         }

//         /// 反序列化帧数据
//         /// </summary>
//         /// <value>The deserialize.</value>
//         public IFrameDeserialize deserialize { get;set; }

//         /// 序列化帧数据
//         /// </summary>
//         /// <value>The sirialize.</value>
//         public IFrameSerialize serialize { get;set; }

//         public DefaultFrameGenerator FrameGenerator { get; set; }


//         private int frameInterval;
//         private int accumulationOfExcess;

//         public FSFrameGenerator_Serialize(FrameDriver frameDriver)
//         {
//             frameInterval = 66;
//             FrameGenerator = new DefaultFrameGenerator(frameDriver, frameInterval);
//         }

//         public FSFrameGenerator_Serialize(FrameDriver frameDriver, int frameInterval)
//         {
//             this.frameInterval = frameInterval;
//             FrameGenerator = new DefaultFrameGenerator(frameDriver, frameInterval);
//         }

//         public virtual void GenerateFrames(int elapsedTime)
//         {
//             if (null!=deserialize)
//             {
//                 accumulationOfExcess += elapsedTime;
//                 int count = accumulationOfExcess / frameInterval;
//                 accumulationOfExcess %= frameInterval;
//                 while (count > 0)
//                 {
//                     count--;
//                     FrameInfoWrapper frameInfoWrapper = deserialize.ReadFrame();
//                     if (null==frameInfoWrapper)
//                     {
//                         //Game.singleton.SetTrigger("back_to_world");
//                         return;
//                     }
//                     DateTime dt = new DateTime(frameInfoWrapper.Ticks);
//                     Debug.Log(string.Format("{0:H:mm:ss:FFF} id:{1}", dt, frameInfoWrapper.FrameId));
//                     FrameInfoPoolable frameInfoPoolable = FS_ClassPool<FrameInfoPoolable>.Get ();
//                     frameInfoWrapper.CopyTo (frameInfoPoolable.frameInfo);
//                     FrameGenerator.OnSyncFrameReceived(frameInfoPoolable);
//                 }
//                 FrameGenerator.ConsumeFrames(elapsedTime);
//                 return;
//             }
//             else
//             {
//                 FrameGenerator.GenerateFrames(elapsedTime);
//                 FrameGenerator.ConsumeFrames(elapsedTime);
//                 return;
//             }
//         }

//         /// <summary>
//         /// 帧被dispath之后的回调。
//         /// </summary>
//         /// <param name="frame"></param>
//         public virtual void OnFrameDispatched(Frame frame)
//         {

//             if (null != FrameGenerator.DestroyFrameData)
//             {
//                 FrameGenerator.DestroyFrameData(frame.data);
//             }
//         }
//     }
// }
