namespace FixedMath
{
    public struct FMatrix3x3
    {
        public Fix64 m00, m01, m02;
        public Fix64 m10, m11, m12;
        public Fix64 m20, m21, m22;

        

        public static readonly FMatrix3x3 identity = new FMatrix3x3(Fix64.one, Fix64.zero, Fix64.zero,
                                                                    Fix64.zero, Fix64.one, Fix64.zero,
                                                                    Fix64.zero, Fix64.zero, Fix64.one);
        public static readonly FMatrix3x3 zero = new FMatrix3x3(Fix64.zero, Fix64.zero, Fix64.zero,
                                                                Fix64.zero, Fix64.zero, Fix64.zero,
                                                                Fix64.zero, Fix64.zero, Fix64.zero);

        internal static FMatrix3x3 InternalIdentity = identity;

        public FMatrix3x3(Fix64 m00, Fix64 m01, Fix64 m02, 
                          Fix64 m10, Fix64 m11, Fix64 m12, 
                          Fix64 m20, Fix64 m21, Fix64 m22)
        {
            this.m00 = m00; 
            this.m01 = m01; 
            this.m02 = m02;
            this.m10 = m10; 
            this.m11 = m11; 
            this.m12 = m12;
            this.m20 = m20; 
            this.m21 = m21; 
            this.m22 = m22;
        }



        /// <summary>
        /// CreateRotationX
        /// </summary>
        /// <param name="xRad">angle x-axis</param>
        /// <param name="result">output matrix represent orientention</param>
        public static void CreateRotationX(Fix64 xRad, out FMatrix3x3 result)
        {
            /// the Rotation xRad Matrix
            //  1    0     0
            //  0   cosx  sinx
            //  0  -sinx  cosx
            Fix64 cosx = Fix64.Cos(xRad);
            Fix64 sinx = Fix64.Sin(xRad);

            result.m00 = Fix64.one; 
            result.m01 = Fix64.zero; 
            result.m02 = Fix64.zero;
            result.m10 = Fix64.zero; 
            result.m11 = cosx; 
            result.m12 = sinx;
            result.m20 = Fix64.zero; 
            result.m21 = -sinx; 
            result.m22 = cosx;
        }

        public static FMatrix3x3 CreateRotationX(Fix64 xRad)
        {
            FMatrix3x3 matrix;

            CreateRotationX(xRad, out matrix);
            return matrix;
        }

        /// <summary>
        /// CreateRotationY
        /// </summary>
        /// <param name="yRad">angle y-axis</param>
        /// <param name="result">output matrix represent orientention</param>
        public static void CreateRotationY(Fix64 yRad, out FMatrix3x3 result)
        {
            //  cosy    0   -siny
            //  0       1   0
            //  siny    0   cosy
            Fix64 cosy = Fix64.Cos(yRad);
            Fix64 siny = Fix64.Sin(yRad);

            result.m00 = cosy; 
            result.m01 = Fix64.zero; 
            result.m02 = -siny;
            result.m10 = Fix64.zero; 
            result.m11 = Fix64.one; 
            result.m12 = Fix64.zero;
            result.m20 = siny; 
            result.m21 = Fix64.zero; 
            result.m22 = cosy;
        }

        public static FMatrix3x3 CreateRotationY(Fix64 yRad)
        {
            FMatrix3x3 matrix;

            CreateRotationY(yRad, out matrix);
            return matrix;
        }



        /// <summary>
        /// CreateRotationZ
        /// </summary>
        /// <param name="zRad">angle z-axis</param>
        /// <param name="matrix">output matrix represent orientention</param>
        public static void CreateRotationZ(Fix64 zRad, out FMatrix3x3 matrix)
        {
            //  cosz    sinz   0
            //  -sinz   cosz   0
            //   0       0     1
            Fix64 cosz = Fix64.Cos(zRad);
            Fix64 sinz = Fix64.Sin(zRad);

            matrix.m00 = cosz; 
            matrix.m01 = sinz;
            matrix.m02 = Fix64.zero;
            matrix.m10 = -sinz;
            matrix.m11 = cosz; 
            matrix.m12 = Fix64.zero;
            matrix.m20 = Fix64.zero; 
            matrix.m21 = Fix64.zero; 
            matrix.m22 = Fix64.one;
        }

        public static FMatrix3x3 CreateRotationZ(Fix64 zRad)
        {
            FMatrix3x3 matrix;
            CreateRotationZ(zRad, out matrix);
            return matrix;
        }

        /// <summary>
        /// Multiply two matrix
        /// </summary>
        /// <param name="matrix1">The first matrix.</param>
        /// <param name="matrix2">The second matrix.</param>
        /// <param name="result">The product of both matrix.</param>
        public static void Multiply(ref FMatrix3x3 matrix1, ref FMatrix3x3 matrix2, out FMatrix3x3 result)
        {
            result.m00 = ((matrix1.m00 * matrix2.m00) + (matrix1.m01 * matrix2.m10)) + (matrix1.m02 * matrix2.m20);
            result.m01 = ((matrix1.m00 * matrix2.m01) + (matrix1.m01 * matrix2.m11)) + (matrix1.m02 * matrix2.m21);
            result.m02 = ((matrix1.m00 * matrix2.m02) + (matrix1.m01 * matrix2.m12)) + (matrix1.m02 * matrix2.m22);

            result.m10 = ((matrix1.m10 * matrix2.m00) + (matrix1.m11 * matrix2.m10)) + (matrix1.m12 * matrix2.m20);
            result.m11 = ((matrix1.m10 * matrix2.m01) + (matrix1.m11 * matrix2.m11)) + (matrix1.m12 * matrix2.m21);
            result.m12 = ((matrix1.m10 * matrix2.m02) + (matrix1.m11 * matrix2.m12)) + (matrix1.m12 * matrix2.m22);

            result.m20 = ((matrix1.m20 * matrix2.m00) + (matrix1.m21 * matrix2.m10)) + (matrix1.m22 * matrix2.m20);
            result.m21 = ((matrix1.m20 * matrix2.m01) + (matrix1.m21 * matrix2.m11)) + (matrix1.m22 * matrix2.m21);
            result.m22 = ((matrix1.m20 * matrix2.m02) + (matrix1.m21 * matrix2.m12)) + (matrix1.m22 * matrix2.m22);
        }

        public static FMatrix3x3 Multiply(FMatrix3x3 matrix1, FMatrix3x3 matrix2)
        {
            FMatrix3x3 result;

            Multiply(ref matrix1, ref matrix2, out result);
            return result;
        }

        /// <summary>
        /// Matrix Add.
        /// </summary>
        /// <param name="matrix1">The first matrix.</param>
        /// <param name="matrix2">The second matrix.</param>
        /// <param name="result">The sum of both matrix.</param>
        public static void Add(ref FMatrix3x3 matrix1, ref FMatrix3x3 matrix2, out FMatrix3x3 result)
        {
            result.m00 = matrix1.m00 + matrix2.m00;
            result.m01 = matrix1.m01 + matrix2.m01;
            result.m02 = matrix1.m02 + matrix2.m02;

            result.m10 = matrix1.m10 + matrix2.m10;
            result.m11 = matrix1.m11 + matrix2.m11;
            result.m12 = matrix1.m12 + matrix2.m12;

            result.m20 = matrix1.m20 + matrix2.m20;
            result.m21 = matrix1.m21 + matrix2.m21;
            result.m22 = matrix1.m22 + matrix2.m22;
        }

        public static FMatrix3x3 Add(FMatrix3x3 matrix1, FMatrix3x3 matrix2)
        {
            FMatrix3x3 result;
            Add(ref matrix1, ref matrix2, out result);
            return result;
        }

        //行列式
        public Fix64 Determinant()
        {
            return m00 * m11 * m22 - m00 * m21 * m12 - m01 * m10 * m22 + m01 * m20 * m12
                + m02 * m10 * m21 - m02 * m20 * m11;
        }

        /// <summary>
        /// Invert Matrix
        /// </summary>
        /// <param name="matrix">The matrix to invert.</param>
        /// <param name="result">The inverted Matrix.</param>
        public static bool Invert(ref FMatrix3x3 matrix, out FMatrix3x3 result)
        {
            Fix64 det = 1024 * matrix.Determinant();

            Fix64 c00 = 1024 * matrix.m11 * matrix.m22 - 1024 * matrix.m12 * matrix.m21;
            Fix64 c01 = 1024 * matrix.m02 * matrix.m21 - 1024 * matrix.m01 * matrix.m22;
            Fix64 c02 = 1024 * matrix.m01 * matrix.m12 - 1024 * matrix.m11 * matrix.m02;

            Fix64 c10 = 1024 * matrix.m12 * matrix.m20 - 1024 * matrix.m22 * matrix.m10;
            Fix64 c11 = 1024 * matrix.m00 * matrix.m22 - 1024 * matrix.m20 * matrix.m02;
            Fix64 c12 = 1024 * matrix.m02 * matrix.m10 - 1024 * matrix.m12 * matrix.m00;

            Fix64 c20 = 1024 * matrix.m10 * matrix.m21 - 1024 * matrix.m20 * matrix.m11;
            Fix64 c21 = 1024 * matrix.m01 * matrix.m20 - 1024 * matrix.m21 * matrix.m00;
            Fix64 c22 = 1024 * matrix.m00 * matrix.m11 - 1024 * matrix.m10 * matrix.m01;

            if (det == Fix64.zero)
            {
                result = FMatrix3x3.identity;
                return false;
            }
            else
            {
                result.m00 = c00 / det;
                result.m01 = c01 / det;
                result.m02 = c02 / det;
                result.m10 = c10 / det;
                result.m11 = c11 / det;
                result.m12 = c12 / det;
                result.m20 = c20 / det;
                result.m21 = c21 / det;
                result.m22 = c22 / det;
                return true;
            }
        }

        public static FMatrix3x3 Invert(ref FMatrix3x3 matrix)
        {
            FMatrix3x3 result;
            Invert(ref matrix, out result);
            return result;
        }

        /// <summary>
        /// Multiply a matrix by a scalar.
        /// </summary>
        /// <param name="matrix1">The matrix.</param>
        /// <param name="num">The scale factor.</param>
        /// <param name="result">A Matrix multiplied by a scalar.</param>
        public static void Multiply(ref FMatrix3x3 matrix1, Fix64 num, out FMatrix3x3 result)
        {
            result.m00 = matrix1.m00 * num;
            result.m01 = matrix1.m01 * num;
            result.m02 = matrix1.m02 * num;
            result.m10 = matrix1.m10 * num;
            result.m11 = matrix1.m11 * num;
            result.m12 = matrix1.m12 * num;
            result.m20 = matrix1.m20 * num;
            result.m21 = matrix1.m21 * num;
            result.m22 = matrix1.m22 * num;
        }

        public static FMatrix3x3 Multiply(FMatrix3x3 matrix1, Fix64 num)
        {
            FMatrix3x3 result;
            Multiply(ref matrix1, num, out result);
            return result;
        }

        /// <summary>
        /// Creates a Matrix representing an orientation from a quaternion.
        /// </summary>
        /// <returns>Matrix representing an orientation.</returns>
        public static FMatrix3x3 CreateFromLookAt(FVector3 position, FVector3 target)
        {
            FMatrix3x3 result;
            LookAt(target - position, FVector3.up, out result);
            return result;
        }

        public static FMatrix3x3 LookAt(FVector3 forward, FVector3 upwards)
        {
            FMatrix3x3 result;
            LookAt(forward, upwards, out result);

            return result;
        }

        public static void LookAt(FVector3 forward, FVector3 upwards, out FMatrix3x3 result)
        {
            FVector3 zaxis = forward; 
            zaxis.Normalize();
            FVector3 xaxis = FVector3.Cross(upwards, zaxis); 
            xaxis.Normalize();
            FVector3 yaxis = FVector3.Cross(zaxis, xaxis);

            result.m00 = xaxis.x;
            result.m10 = yaxis.x;
            result.m20 = zaxis.x;
            result.m01 = xaxis.y;
            result.m11 = yaxis.y;
            result.m21 = zaxis.y;
            result.m02 = xaxis.z;
            result.m12 = yaxis.z;
            result.m22 = zaxis.z;
        }

        public static FMatrix3x3 CreateFromQuaternion(FQuaternion quaternion)
        {
            FMatrix3x3 result;
            CreateFromQuaternion(ref quaternion, out result);
            return result;
        }

        public static void CreateFromQuaternion(ref FQuaternion quaternion, out FMatrix3x3 result)
        {
            QuaternionToMatrix(ref quaternion, out result);
        }

        /// <summary>
        /// Creates a Matrix representing an orientation from a quaternion.
        /// </summary>
        /// <param name="quaternion">The quaternion the matrix should be created from.</param>
        /// <param name="result">Matrix representing an orientation.</param>
        //  1-2y^2-2z^2    2xy+2wz       2xz-2wy
        //  2xy-2wz        1-2x^2-2z^2   2yz+2wx
        //  2xz+2wy        2yz-2wx       1-2x^2-2y^2
        public static void QuaternionToMatrix(ref FQuaternion quaternion, out FMatrix3x3 result)
        {
            Fix64 x2 = quaternion.x * quaternion.x;    //9
            Fix64 y2 = quaternion.y * quaternion.y;    //8
            Fix64 z2 = quaternion.z * quaternion.z;    //7
            Fix64 xy = quaternion.x * quaternion.y;    //6
            Fix64 zw = quaternion.z * quaternion.w;    //5
            Fix64 zx = quaternion.z * quaternion.x;    //4
            Fix64 yw = quaternion.y * quaternion.w;    //3
            Fix64 yz = quaternion.y * quaternion.z;    //2
            Fix64 xw = quaternion.x * quaternion.w;    //1

            result.m00 = Fix64.one - (2 * (y2 + z2));
            result.m01 = 2 * (xy + zw);
            result.m02 = 2 * (zx - yw);
            result.m10 = 2 * (xy - zw);
            result.m11 = Fix64.one - (2 * (x2 + z2));
            result.m12 = 2 * (yz + xw);
            result.m20 = 2 * (zx + yw);
            result.m21 = 2 * (yz - xw);
            result.m22 = Fix64.one - (2 * (x2 + y2));
        }

        /// <summary>
        /// Creates the transposed matrix.
        /// </summary>
        /// <param name="matrix">The matrix which should be transposed.</param>
        /// <param name="result">The transposed Matrix.</param>
        public static void Transpose(ref FMatrix3x3 matrix, out FMatrix3x3 result)
        {
            result.m00 = matrix.m00;
            result.m01 = matrix.m10;
            result.m02 = matrix.m20;
            result.m10 = matrix.m01;
            result.m11 = matrix.m11;
            result.m12 = matrix.m21;
            result.m20 = matrix.m02;
            result.m21 = matrix.m12;
            result.m22 = matrix.m22;
        }

        public static FMatrix3x3 Transpose(FMatrix3x3 matrix)
        {
            FMatrix3x3 result;
            Transpose(ref matrix, out result);
            return result;
        }

        public Fix64 Trace()
        {
            return this.m00 + this.m11 + this.m22;
        }

        #region operator override

        public static FMatrix3x3 operator *(FMatrix3x3 value1, FMatrix3x3 value2)
        {
            FMatrix3x3 result; 
            Multiply(ref value1, ref value2, out result);
            return result;
        }

        public static FMatrix3x3 operator *(Fix64 f, FMatrix3x3 matrix)
        {
            return Multiply(matrix, f);
        }

        public static FMatrix3x3 operator *(FMatrix3x3 matrix, Fix64 f)
        {
            return Multiply(matrix, f);
        }


        public static FVector3 operator *(FVector3 v3, FMatrix3x3 matrix)
        {
            FVector3 result;
            result.x = v3.x * matrix.m00 + v3.y * matrix.m10 + v3.z * matrix.m20;
            result.y = v3.x * matrix.m01 + v3.y * matrix.m11 + v3.z * matrix.m21;
            result.z = v3.x * matrix.m02 + v3.y * matrix.m12 + v3.z * matrix.m22;
            return result;
        }

        public static FMatrix3x3 operator +(FMatrix3x3 value1, FMatrix3x3 value2)
        {
            FMatrix3x3 result; 
            Add(ref value1, ref value2, out result);
            return result;
        }

        public static FMatrix3x3 operator -(FMatrix3x3 value1, FMatrix3x3 value2)
        {
            FMatrix3x3 result;
            Multiply(ref value2, -Fix64.one, out value2);
            Add(ref value1, ref value2, out result);
            return result;
        }


        public static bool operator ==(FMatrix3x3 value1, FMatrix3x3 value2)
        {
            return value1.m00 == value2.m00 &&
                value1.m01 == value2.m01 &&
                value1.m02 == value2.m02 &&
                value1.m10 == value2.m10 &&
                value1.m11 == value2.m11 &&
                value1.m12 == value2.m12 &&
                value1.m20 == value2.m20 &&
                value1.m21 == value2.m21 &&
                value1.m22 == value2.m22;
        }

        public static bool operator !=(FMatrix3x3 value1, FMatrix3x3 value2)
        {
            return !(value1 == value2);
        }

        #endregion

        public override bool Equals(object obj)
        {
            if (!(obj is FMatrix3x3)) return false;
            FMatrix3x3 other = (FMatrix3x3)obj;

            return this == other;
        }

        public override int GetHashCode()
        {
            return m00.GetHashCode() ^
                m01.GetHashCode() ^
                m02.GetHashCode() ^
                m10.GetHashCode() ^
                m11.GetHashCode() ^
                m12.GetHashCode() ^
                m20.GetHashCode() ^
                m21.GetHashCode() ^
                m22.GetHashCode();
        }


        //public static void CreateFromAxisAngle(ref TSVector axis, FP angle, out TSMatrix result)
        //{
        //    FP x = axis.x;
        //    FP y = axis.y;
        //    FP z = axis.z;
        //    FP num2 = FP.Sin(angle);
        //    FP num = FP.Cos(angle);
        //    FP num11 = x * x;
        //    FP num10 = y * y;
        //    FP num9 = z * z;
        //    FP num8 = x * y;
        //    FP num7 = x * z;
        //    FP num6 = y * z;
        //    result.M11 = num11 + (num * (FP.One - num11));
        //    result.M12 = (num8 - (num * num8)) + (num2 * z);
        //    result.M13 = (num7 - (num * num7)) - (num2 * y);
        //    result.M21 = (num8 - (num * num8)) - (num2 * z);
        //    result.M22 = num10 + (num * (FP.One - num10));
        //    result.M23 = (num6 - (num * num6)) + (num2 * x);
        //    result.M31 = (num7 - (num * num7)) + (num2 * y);
        //    result.M32 = (num6 - (num * num6)) - (num2 * x);
        //    result.M33 = num9 + (num * (FP.One - num9));
        //}

        //public static TSMatrix AngleAxis(FP angle, TSVector axis)
        //{
        //    TSMatrix result; CreateFromAxisAngle(ref axis, angle, out result);
        //    return result;
        //}

        public override string ToString()
        {
            return string.Format("{0}|{1}|{2}\n{3}|{4}|{5}\n{6}|{7}|{8}", m00, m01, m02, m10, m11, m12, m20, m21, m22);
        }
    }
}
