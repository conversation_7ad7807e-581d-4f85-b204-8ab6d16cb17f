﻿///////////////////////////////////////////////////////////////////////////////
/// author : taodeng
/// time :   2017-4-11 11:19
/// version: 1.0
///////////////////////////////////////////////////////////////////////////////

namespace Common.FrameDriving
{
    /// <summary>
    /// 帧产生器。
    /// 可以impl该接口类，实现不同的帧产生策略，进行帧同步、状态同步、单机以及各种模拟。
    /// </summary>
    public interface IFrameGenerator
    {
        FrameDriver frameDriver { get; }

        /// <summary>
        /// 生成帧。
        /// </summary>
        /// <param name="frameDriver"></param>
        /// <param name="gameTime"></param>
        void GenerateFrames(int elapsedTime);

        /// <summary>
        /// 帧被dispath之后的回调。
        /// </summary>
        /// <param name="frame"></param>
        void OnFrameDispatched(Frame frame);
    }
}

