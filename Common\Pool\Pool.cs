using System;
using System.Collections.Generic;

namespace GP.Pool
{
    internal sealed class Pool<T> : IPool 
        where T : IPoolable, new()
    {
        private Stack<T> m_PooledObjects = new Stack<T>();
        internal T Get()
        {
            if (m_PooledObjects.Count > 0)
            {
                return m_PooledObjects.Pop();
            }

            return new T();
        }

        internal void Free(T obj)
        {
            obj.Reset();
            m_PooledObjects.Push(obj);
        }

        public void Reset() 
        {
            m_PooledObjects.Clear();
        }
    }

    internal static class PoolMgr 
    {
        static List<IPool> sPools = new List<IPool>();

        internal static Pool<T> GetPool<T>(bool create = true) where T : IPoolable, new()
        {
            foreach (var pool in sPools)
            {
                if (pool is Pool<T> p) return p;
            }

            if (create)
            {
                var pool = new Pool<T>();
                sPools.Add(pool);
                return pool;
            }

            return null;
        }

        internal static void Clear()
        {
            foreach (var pool in sPools)
            {
                pool.Reset();
            }

            sPools.Clear();
        }
    }

    public static class PoolAPI 
    {
        public static T Get<T>() where T : IPoolable, new()
        {
            var pool = PoolMgr.GetPool<T>();
            return pool.Get();
        }

        public static void Free<T>(T obj) where T : IPoolable, new()
        {
            var pool = PoolMgr.GetPool<T>();
            pool.Free(obj);
        }

        public static void Clear<T>() where T : IPoolable, new()
        {
            var pool = PoolMgr.GetPool<T>(false);
            if (pool != null) pool.Reset();
        }

        public static void ClearAll()
        {
            PoolMgr.Clear();
        }
    }
}