﻿This 2D/3D physics library is a custom version of farseer (https://farseerphysics.codeplex.com/) / jitter physics (http://code.google.com/p/jitterphysics);

The extensive modifications include:
- making the code detrministic (change from float to FixedPoint, no random numbers, stable sorting, etc);
- custom Unity wrapper components and property drawers;

Original open source license files are included as requested.