﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

#region Using Statements
using FixedMath;
using System;
using System.Collections.Generic;
#endregion

namespace Common.Physics {

    /// <summary>
    /// Gets called when a shape changes one of the parameters.
    /// For example the size of a box is changed.
    /// </summary>
    public delegate void ShapeUpdatedHandler();

    /// <summary>
    /// Represents the collision part of the RigidBody. A shape is mainly definied through it's supportmap.
    /// Shapes represent convex objects. Inherited classes have to overwrite the supportmap function.
    /// To implement you own shape: derive a class from <see cref="Shape"/>, implement the support map function
    /// and call 'UpdateShape' within the constructor. GeometricCenter, Mass, BoundingBox and Inertia is calculated numerically
    /// based on your SupportMap implementation.
    /// </summary>
    public abstract class Shape : ISupportMappable
    {

        // internal values so we can access them fast  without calling properties.
        internal FMatrix3x3 inertia = FMatrix3x3.identity;
        internal Fix64 mass = Fix64.one;

        internal DBBox boundingBox = DBBox.LargeBox;
        internal FVector3 geomCen = FVector3.zero;

        /// <summary>
        /// Gets called when the shape changes one of the parameters.
        /// </summary>
        public event ShapeUpdatedHandler ShapeUpdated;

        /// <summary>
        /// Creates a new instance of a shape.
        /// </summary>
        public Shape()
        {
        }

        /// <summary>
        /// Returns the inertia of the untransformed shape.
        /// </summary>
        public FMatrix3x3 Inertia { get { return inertia; } protected set { inertia = value; } }


        /// <summary>
        /// Gets the mass of the shape. This is the volume. (density = 1)
        /// </summary>
        public Fix64 Mass { get { return mass; } protected set { mass = value; } }

        /// <summary>
        /// Informs all listener that the shape changed.
        /// </summary>
        protected void RaiseShapeUpdated()
        {
            if (ShapeUpdated != null) ShapeUpdated();
        }

        /// <summary>
        /// The untransformed axis aligned bounding box of the shape.
        /// </summary>
        public DBBox BoundingBox { get { return boundingBox; } }

        /// <summary>
        /// Allows to set a user defined value to the shape.
        /// </summary>
        public object Tag { get; set; }

        private struct ClipTriangle
        {
            public FVector3 n1;
            public FVector3 n2;
            public FVector3 n3;
            public int generation;
        };

        /// <summary>
        /// Hull making.
        /// </summary>
        /// <remarks>Based/Completely from http://www.xbdev.net/physics/MinkowskiDifference/index.php
        /// I don't (100%) see why this should always work.
        /// </remarks>
        /// <param name="triangleList"></param>
        /// <param name="generationThreshold"></param>
        public virtual void MakeHull(ref List<FVector3> triangleList, int generationThreshold)
        {
            Fix64 distanceThreshold = Fix64.zero;

            if (generationThreshold < 0) generationThreshold = 4;

            Stack<ClipTriangle> activeTriList = new Stack<ClipTriangle>();

            FVector3[] v = new FVector3[] // 6 Array
		    {
			new FVector3( -1,  0,  0 ),
			new FVector3(  1,  0,  0 ),

			new FVector3(  0, -1,  0 ),
			new FVector3(  0,  1,  0 ),

			new FVector3(  0,  0, -1 ),
			new FVector3(  0,  0,  1 ),
		    };

            int[,] kTriangleVerts = new int[8, 3] // 8 x 3 Array
		    {
			{ 5, 1, 3 },
			{ 4, 3, 1 },
			{ 3, 4, 0 },
			{ 0, 5, 3 },

			{ 5, 2, 1 },
			{ 4, 1, 2 },
			{ 2, 0, 4 },
			{ 0, 2, 5 }
		    };

            for (int i = 0; i < 8; i++)
            {
                ClipTriangle tri = new ClipTriangle();
                tri.n1 = v[kTriangleVerts[i, 0]];
                tri.n2 = v[kTriangleVerts[i, 1]];
                tri.n3 = v[kTriangleVerts[i, 2]];
                tri.generation = 0;
                activeTriList.Push(tri);
            }

            // surfaceTriList
            while (activeTriList.Count > 0)
            {
                ClipTriangle tri = activeTriList.Pop();

                FVector3 p1; SupportMapping(ref tri.n1, out p1);
                FVector3 p2; SupportMapping(ref tri.n2, out p2);
                FVector3 p3; SupportMapping(ref tri.n3, out p3);

                Fix64 d1 = (p2 - p1).sqrMagnitude;
                Fix64 d2 = (p3 - p2).sqrMagnitude;
                Fix64 d3 = (p1 - p3).sqrMagnitude;

                if (Fix64.Max(Fix64.Max(d1, d2), d3) > distanceThreshold && tri.generation < generationThreshold)
                {
                    ClipTriangle tri1 = new ClipTriangle();
                    ClipTriangle tri2 = new ClipTriangle();
                    ClipTriangle tri3 = new ClipTriangle();
                    ClipTriangle tri4 = new ClipTriangle();

                    tri1.generation = tri.generation + 1;
                    tri2.generation = tri.generation + 1;
                    tri3.generation = tri.generation + 1;
                    tri4.generation = tri.generation + 1;

                    tri1.n1 = tri.n1;
                    tri2.n2 = tri.n2;
                    tri3.n3 = tri.n3;

                    FVector3 n = Fix64.half * (tri.n1 + tri.n2);
                    n.Normalize();

                    tri1.n2 = n;
                    tri2.n1 = n;
                    tri4.n3 = n;

                    n = Fix64.half * (tri.n2 + tri.n3);
                    n.Normalize();

                    tri2.n3 = n;
                    tri3.n2 = n;
                    tri4.n1 = n;

                    n = Fix64.half * (tri.n3 + tri.n1);
                    n.Normalize();

                    tri1.n3 = n;
                    tri3.n1 = n;
                    tri4.n2 = n;

                    activeTriList.Push(tri1);
                    activeTriList.Push(tri2);
                    activeTriList.Push(tri3);
                    activeTriList.Push(tri4);
                }
                else
                {
                    //if (((p3 - p1) % (p2 - p1)).sqrMagnitude > DMath.Epsilon)
                    
                    if ((FVector3.Cross(p3 - p1, p2 - p1)).sqrMagnitude > DMath.Epsilon)
                    {
                        triangleList.Add(p1);
                        triangleList.Add(p2);
                        triangleList.Add(p3);
                    }
                }
            }
        }


        /// <summary>
        /// Uses the supportMapping to calculate the bounding box. Should be overidden
        /// to make this faster.
        /// </summary>
        /// <param name="orientation">The orientation of the shape.</param>
        /// <param name="box">The resulting axis aligned bounding box.</param>
        public virtual void GetBoundingBox(ref FMatrix3x3 orientation, out DBBox box)
        {
            // I don't think that this can be done faster.
            // 6 is the minimum number of SupportMap calls.

            FVector3 vec = FVector3.zero;

            vec.Set(orientation.m00, orientation.m10, orientation.m20);
            SupportMapping(ref vec, out vec);
            box.max.x = orientation.m00 * vec.x + orientation.m10 * vec.y + orientation.m20 * vec.z;

            vec.Set(orientation.m01, orientation.m11, orientation.m21);
            SupportMapping(ref vec, out vec);
            box.max.y = orientation.m01 * vec.x + orientation.m11 * vec.y + orientation.m21 * vec.z;

            vec.Set(orientation.m02, orientation.m12, orientation.m22);
            SupportMapping(ref vec, out vec);
            box.max.z = orientation.m02 * vec.x + orientation.m12 * vec.y + orientation.m22 * vec.z;

            vec.Set(-orientation.m00, -orientation.m10, -orientation.m20);
            SupportMapping(ref vec, out vec);
            box.min.x = orientation.m00 * vec.x + orientation.m10 * vec.y + orientation.m20 * vec.z;

            vec.Set(-orientation.m01, -orientation.m11, -orientation.m21);
            SupportMapping(ref vec, out vec);
            box.min.y = orientation.m01 * vec.x + orientation.m11 * vec.y + orientation.m21 * vec.z;

            vec.Set(-orientation.m02, -orientation.m12, -orientation.m22);
            SupportMapping(ref vec, out vec);
            box.min.z = orientation.m02 * vec.x + orientation.m12 * vec.y + orientation.m22 * vec.z;
        }

        /// <summary>
        /// This method uses the <see cref="ISupportMappable"/> implementation
        /// to calculate the local bounding box, the mass, geometric center and 
        /// the inertia of the shape. In custom shapes this method should be overidden
        /// to compute this values faster.
        /// </summary>
        public virtual void UpdateShape()
        {
            GetBoundingBox(ref FMatrix3x3.InternalIdentity, out boundingBox);

            CalculateMassInertia();
            RaiseShapeUpdated();
        }
        
        /// <summary>
        /// Calculates the inertia of the shape relative to the center of mass.
        /// </summary>
        /// <param name="shape"></param>
        /// <param name="centerOfMass"></param>
        /// <param name="inertia">Returns the inertia relative to the center of mass, not to the origin</param>
        /// <returns></returns>
        #region  public static FP CalculateMassInertia(Shape shape, out Vector centerOfMass, out Matrix inertia)
        public static Fix64 CalculateMassInertia(Shape shape, out FVector3 centerOfMass,
            out FMatrix3x3 inertia)
        {
            Fix64 mass = Fix64.zero;
            centerOfMass = FVector3.zero; inertia = FMatrix3x3.zero;

            if (shape is Multishape) throw new ArgumentException("Can't calculate inertia of multishapes.", "shape");

            // build a triangle hull around the shape
            List<FVector3> hullTriangles = new List<FVector3>();
            shape.MakeHull(ref hullTriangles, 3);

            // create inertia of tetrahedron with vertices at
            // (0,0,0) (1,0,0) (0,1,0) (0,0,1)
            Fix64 a = Fix64.one / (60 * Fix64.one), b = Fix64.one / (120 * Fix64.one);
            FMatrix3x3 C = new FMatrix3x3(a, b, b, b, a, b, b, b, a);

            for (int i = 0; i < hullTriangles.Count; i += 3)
            {
                FVector3 column0 = hullTriangles[i + 0];
                FVector3 column1 = hullTriangles[i + 1];
                FVector3 column2 = hullTriangles[i + 2];

                FMatrix3x3 A = new FMatrix3x3(column0.x, column1.x, column2.x,
                    column0.y, column1.y, column2.y,
                    column0.z, column1.z, column2.z);

                Fix64 detA = A.Determinant();

                // now transform this canonical tetrahedron to the target tetrahedron
                // inertia by a linear transformation A
                FMatrix3x3 tetrahedronInertia = FMatrix3x3.Multiply(A * C * FMatrix3x3.Transpose(A), detA);

                FVector3 tetrahedronCOM = (Fix64.one / (4 * Fix64.one)) * (hullTriangles[i + 0] + hullTriangles[i + 1] + hullTriangles[i + 2]);
                Fix64 tetrahedronMass = (Fix64.one / (6 * Fix64.one)) * detA;

                inertia += tetrahedronInertia;
                centerOfMass += tetrahedronMass * tetrahedronCOM;
                mass += tetrahedronMass;
            }

            inertia = FMatrix3x3.Multiply(FMatrix3x3.identity, inertia.Trace()) - inertia;
            centerOfMass = centerOfMass * (Fix64.one / mass);

            Fix64 x = centerOfMass.x;
            Fix64 y = centerOfMass.y;
            Fix64 z = centerOfMass.z;

            // now translate the inertia by the center of mass
            FMatrix3x3 t = new FMatrix3x3(
                -mass * (y * y + z * z), mass * x * y, mass * x * z,
                mass * y * x, -mass * (z * z + x * x), mass * y * z,
                mass * z * x, mass * z * y, -mass * (x * x + y * y));

            FMatrix3x3.Add(ref inertia, ref t, out inertia);

            return mass;
        }
        #endregion
        
        /// <summary>
        /// Numerically calculates the inertia, mass and geometric center of the shape.
        /// This gets a good value for "normal" shapes. The algorithm isn't very accurate
        /// for very flat shapes. 
        /// </summary>
        public virtual void CalculateMassInertia()
        {
            this.mass = Shape.CalculateMassInertia(this, out geomCen, out inertia);
        }

        /// <summary>
        /// SupportMapping. Finds the point in the shape furthest away from the given direction.
        /// Imagine a plane with a normal in the search direction. Now move the plane along the normal
        /// until the plane does not intersect the shape. The last intersection point is the result.
        /// </summary>
        /// <param name="direction">The direction.</param>
        /// <param name="result">The result.</param>
        public abstract void SupportMapping(ref FVector3 direction, out FVector3 result);

        /// <summary>
        /// The center of the SupportMap.
        /// </summary>
        /// <param name="geomCenter">The center of the SupportMap.</param>
        public void SupportCenter(out FVector3 geomCenter)
        {
            geomCenter = this.geomCen;
        }

    }
}
