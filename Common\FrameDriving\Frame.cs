﻿
namespace Common.FrameDriving
{
    /// <summary>
    /// 帧数据结构
    /// </summary>
    public class Frame
    {
        /// <summary>
        /// 帧序号
        /// </summary>
        public uint seqNum { get; set; }

        /// <summary>
        /// 本帧的时间片，单位是毫秒。(帧同步时，时间片长度是固定的。)
        /// </summary>
        public int timeSpan { get; set; }

        /// <summary>
        /// 帧上绑定的数据。
        /// </summary>
        public object data { get; set; }

        public void Reset()
        {
            seqNum = 0;
            timeSpan = 0;
            data = null;
        }
    }
}
