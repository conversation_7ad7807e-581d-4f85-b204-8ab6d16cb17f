﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

#region Using Statements
using FixedMath;
using System;
#endregion

namespace Common.Physics {

    /// <summary>
    /// A <see cref="Shape"/> representing a capsule.
    /// </summary>
    public class CapsuleShape : Shape
    {
        internal Fix64 length, radius;

        /// <summary>
        /// Gets or sets the length of the capsule (exclusive the round endcaps).
        /// </summary>
        public Fix64 Length { get { return length; } set { length = value; UpdateShape(); } }

        /// <summary>
        /// Gets or sets the radius of the endcaps.
        /// </summary>
        public Fix64 Radius { get { return radius; } set { radius = value; UpdateShape(); } }

        /// <summary>
        /// Create a new instance of the capsule.
        /// </summary>
        /// <param name="length">The length of the capsule (exclusive the round endcaps).</param>
        /// <param name="radius">The radius of the endcaps.</param>
        public CapsuleShape(Fix64 length,Fix64 radius)
        {
            this.length = length;
            this.radius = radius;
            UpdateShape();
        }

        /// <summary>
        /// 
        /// </summary>
        public override void CalculateMassInertia()
        {
            Fix64 massSphere = ( (3 * Fix64.one) / (4 * Fix64.one)) * DMath.Pi * radius * radius * radius;
            Fix64 massCylinder = DMath.Pi * radius * radius * length;

            mass = massCylinder + massSphere;

            this.inertia.m00 = (Fix64.one / (4 * Fix64.one)) * massCylinder * radius * radius + (Fix64.one / (12 * Fix64.one)) * massCylinder * length * length +  ((2 * Fix64.one) / (5 * Fix64.one)) * massSphere * radius * radius + (Fix64.one / (4 * Fix64.one)) * length * length * massSphere;
            this.inertia.m11 = (Fix64.one / (2 * Fix64.one)) * massCylinder * radius * radius +  ((2 * Fix64.one) / (5 * Fix64.one)) * massSphere * radius * radius;
            this.inertia.m22 = (Fix64.one / (4 * Fix64.one)) * massCylinder * radius * radius + (Fix64.one / (12 * Fix64.one)) * massCylinder * length * length +  ((2 * Fix64.one) / (5 * Fix64.one)) * massSphere * radius * radius + (Fix64.one / (4 * Fix64.one)) * length * length * massSphere;

            //this.inertia.m00 = (Fix64.one / (4 * Fix64.one)) * mass * radius * radius + (Fix64.one / (12 * Fix64.one)) * mass * height * height;
            //this.inertia.m11 = (Fix64.one / (2 * Fix64.one)) * mass * radius * radius;
            //this.inertia.m22 = (Fix64.one / (4 * Fix64.one)) * mass * radius * radius + (Fix64.one / (12 * Fix64.one)) * mass * height * height;
        }


        /// <summary>
        /// SupportMapping. Finds the point in the shape furthest away from the given direction.
        /// Imagine a plane with a normal in the search direction. Now move the plane along the normal
        /// until the plane does not intersect the shape. The last intersection point is the result.
        /// </summary>
        /// <param name="direction">The direction.</param>
        /// <param name="result">The result.</param>
        public override void SupportMapping(ref FVector3 direction, out FVector3 result)
        {
            Fix64 r = Fix64.Sqrt(direction.x * direction.x + direction.z * direction.z);

            if (Fix64.Abs(ref direction.y) > Fix64.zero)
            {
                FVector3 dir; FVector3.Normalize(ref direction, out dir);
                FVector3.Multiply(ref dir, radius, out result);
                result.y += Fix64.Sign(direction.y) * Fix64.half * length;              
            }
            else if (r > Fix64.zero)
            {
                result.x = direction.x / r * radius;
                result.y = Fix64.zero;
                result.z = direction.z / r * radius;
            }
            else
            {
                result.x = Fix64.zero;
                result.y = Fix64.zero;
                result.z = Fix64.zero;
            }
        }
    }
}
