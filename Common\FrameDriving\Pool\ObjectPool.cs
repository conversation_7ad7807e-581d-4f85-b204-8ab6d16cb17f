using System.Collections.Generic;

namespace Common.Pool
{
    /// <summary>
    /// 通用的对象池.
    /// </summary>
    public class ObjectPool<T> where T : new()
    {
        public ObjectPool()
        {
        }

        public ObjectPool(IConstructImpl constructImpl)
        {
            this.constructImpl = constructImpl;
        }

        public interface IConstructImpl
        {
            T ConstructObject();
            void ResetObject(T obj);
        }

        public IConstructImpl constructImpl
        {
            get
            {
                if (null != mConstructImpl)
                {
                    return mConstructImpl;
                }
                else
                {
                    return mDefaultConstructImpl;
                }
            }

            set
            {
                mConstructImpl = value;
            }
        }

        private IConstructImpl mConstructImpl;
        private DefaultConstructImpl mDefaultConstructImpl = new DefaultConstructImpl();

        public class DefaultConstructImpl : IConstructImpl
        {
            public T ConstructObject()
            {
                return new T();
            }

            public void ResetObject(T obj)
            {
            }
        }

        public T Alloc()
        {
            if (null != constructImpl)
            {
                T obj = GetObjectFromFreeList();

                if (null != obj)
                {
                    return obj;
                }
                else
                {
                    return constructImpl.ConstructObject();
                }
            }

            return default(T);
        }

        public void Free(T obj)
        {
            if (null != constructImpl)
            {
                constructImpl.ResetObject(obj);
                mFreeList.Push(obj);
            }
        }

        private T GetObjectFromFreeList()
        {
            if (mFreeList.Count > 0)
            {
                return mFreeList.Pop();
            }

            return default(T);
        }

        protected Stack<T> mFreeList = new Stack<T>();
    }
}
