﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net7.0;netstandard2.1</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <NoWarn>$(NoWarn);CS1591;CA2255</NoWarn>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <PackageTags>serializer</PackageTags>
        <Description>Additional streaming support for MemoryPack.</Description>
    </PropertyGroup>

    <ItemGroup>
        <None Include="../../Icon.png" Pack="true" PackagePath="/" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.IO.Pipelines" Version="6.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\MemoryPack.Core\MemoryPack.Core.csproj" />
    </ItemGroup>

</Project>

