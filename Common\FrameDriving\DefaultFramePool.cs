﻿
namespace Common.FrameDriving
{
    using Common.Pool;
    using System;

    public class DefaultFramePool : IFramePool
    {

        ObjectPool<Frame> mImpl;

        class DFPImplement : ObjectPool<Frame>.IConstructImpl
        {
            public Frame ConstructObject()
            {
                return new Frame();
            }

            public void ResetObject(Frame obj)
            {
                obj.Reset();
            }
        }

        public DefaultFramePool()
        {
            mImpl = new ObjectPool<Frame>(new DFPImplement());
        }

        public Frame Alloc()
        {
            return mImpl.Alloc();
        }

        public void Free(Frame frame)
        {
            mImpl.Free(frame);
        }
    }
}


