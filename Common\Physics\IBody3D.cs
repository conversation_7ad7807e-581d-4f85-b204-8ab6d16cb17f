﻿using FixedMath;

namespace Common.Physics
{
    /**
    * @brief Represents an interface to 3D bodies.
    **/
    public interface IBody3D : IBody {

        /**
        * @brief If true the body doesn't move around by collisions.
        **/
        bool TSIsStatic {
            get; set;
        }

        /**
        * @brief Linear drag coeficient.
        **/
        Fix64 TSLinearDrag {
            get; set;
        }

        /**
        * @brief Angular drag coeficient.
        **/
        Fix64 TSAngularDrag {
            get; set;
        }

        /**
         *  @brief Static friction when in contact. 
         **/
        Fix64 TSFriction {
            get; set;
        }

        /**
        * @brief Coeficient of restitution.
        **/
        Fix64 TSRestitution {
            get; set;
        }

        /**
        * @brief Set/get body's position.
        **/
        FVector3 TSPosition {
            get; set;
        }

        /**
        * @brief Set/get body's orientation.
        **/
        FMatrix3x3 TSOrientation {
            get; set;
        }

        /**
        * @brief If true the body is affected by gravity.
        **/
        bool AffectedByGravity {
            get; set;
        }

        /**
        * @brief If true the body is managed as kinematic.
        **/
        bool TSIsKinematic {
            get; set;
        }

        /**
        * @brief Set/get body's linear velocity.
        **/
        FVector3 TSLinearVelocity {
            get; set;
        }

        /**
        * @brief Set/get body's angular velocity.
        **/
        FVector3 TSAngularVelocity {
            get; set;
        }

        /**
        * @brief Applies a force to the body's center.
        **/
        void TSApplyForce(FVector3 force);

        /**
        * @brief Applies a force to the body at a specific position.
        **/
        void TSApplyForce(FVector3 force, FVector3 position);

        /**
        * @brief Applies a impulse to the body's center.
        **/
        void TSApplyImpulse(FVector3 force);

        /**
        * @brief Applies a impulse to the body at a specific position.
        **/
        void TSApplyImpulse(FVector3 force, FVector3 position);

        /**
        * @brief Applies a torque force to the body.
        **/
        void TSApplyTorque(FVector3 force);

        /**
         * @brief Applies a torque force to the body.
         **/
        void TSApplyRelativeTorque(FVector3 force);

    }

}