﻿namespace FixedMath
{
    public struct FVector4
    {
        public Fix64 x;
        public Fix64 y;
        public Fix64 z;
        public Fix64 w;

        public FVector4(Fix64 x, Fix64 y, Fix64 z)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = Fix64.zero;
        }

        public FVector4(Fix64 x, Fix64 y, Fix64 z, Fix64 w)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = w;
        }

        public static FVector4 zero { get { return _zero; } }
        static FVector4 _zero = new FVector4(Fix64.zero, Fix64.zero, Fix64.zero, Fix64.zero);

        public static FVector4 one { get { return _one; } }
        static FVector4 _one = new FVector4(Fix64.one, Fix64.one, Fix64.one, Fix64.one);

        /// <summary>
        /// length of vector.
        /// </summary>
        public Fix64 magnitude
        {
            get
            {
                return (x * x + y * y + z * z + w * w).Sqrt();
            }
        }

        /// <summary>
        /// squared of length.
        /// </summary>
        public Fix64 sqrMagnitude
        {
            get
            {
                return x * x + y * y + z * z + w * w;
            }
        }

        public FVector4 normalized
        {
            get
            {
                Fix64 len = magnitude;
                return new FVector4(x / len, y / len, z / len, w / len);
            }
        }

        public override string ToString()
        {
            return x.ToString() + "," + y.ToString() + "," + z.ToString() + "," + w.ToString();
        }

        public override bool Equals(object obj)
        {
            return ((FVector4)obj).x == x &&
                   ((FVector4)obj).y == y &&
                   ((FVector4)obj).z == z &&
                   ((FVector4)obj).w == w;
        }

        public override int GetHashCode()
        {
            return magnitude.GetHashCode();
        }

        public void Normalize()
        {
            Fix64 len = magnitude;
            x /= len;
            y /= len;
            z /= len;
            w /= len;
        }

        public void Scale(FVector4 scale)
        {
            x *= scale.x;
            y *= scale.y;
            z *= scale.z;
            w *= scale.w;
        }

        public void Set(Fix64 x, Fix64 y, Fix64 z, Fix64 w)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = w;
        }

        public static FVector4 operator +(FVector4 a, FVector4 b)
        {
            return new FVector4(a.x + b.x, a.y + b.y, a.z + b.z, a.w + b.w);
        }

        public static FVector4 operator -(FVector4 a)
        {
            return new FVector4(-a.x, -a.y, -a.z, -a.w);
        }

        public static FVector4 operator -(FVector4 a, FVector4 b)
        {
            return new FVector4(a.x - b.x, a.y - b.y, a.z - b.z, a.w - b.w);
        }

        public static FVector4 operator *(Fix64 d, FVector4 a)
        {
            return new FVector4(a.x * d, a.y * d, a.z * d, a.w * d);
        }

        public static FVector4 operator *(FVector4 a, Fix64 d)
        {
            return new FVector4(a.x * d, a.y * d, a.z * d, a.w * d);
        }

        public static FVector4 operator /(FVector4 a, Fix64 d)
        {
            return new FVector4(a.x / d, a.y / d, a.z / d, a.w / d);
        }

        public static bool operator ==(FVector4 lhs, FVector4 rhs)
        {
            return lhs.x == rhs.x && lhs.y == rhs.y && lhs.z == rhs.z && lhs.w == rhs.w;
        }

        public static bool operator !=(FVector4 lhs, FVector4 rhs)
        {
            return lhs.x != rhs.x || lhs.y != rhs.y || lhs.z != rhs.z || lhs.w != rhs.w;
        }

        public void ClampMagnitude(Fix64 maxLength)
        {
            Fix64 len = magnitude;

            if (len > maxLength)
            {
                // x / magnitude * maxLength, y / magnitude * maxLength
                // x * (maxLength / magnitude), y * (maxLength / magnitude)
                Fix64 scaleFactor = maxLength / len;

                x *= scaleFactor;
                y *= scaleFactor;
                z *= scaleFactor;
                w *= scaleFactor;
            }
        }

        /// <summary>
        /// 求两个向量的欧几里得距离.
        /// </summary>
        /// <returns></returns>
        public static Fix64 Distance(FVector4 a, FVector4 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;
            Fix64 dz = a.z - b.z;
            Fix64 dw = a.w - b.w;

            return (dx * dx + dy * dy + dz * dz + dw * dw).Sqrt();
        }

        /// <summary>
        /// 求两个向量的欧几里得距离的平方.
        /// </summary>
        /// <returns></returns>
        public Fix64 SqrDistance(FVector4 a, FVector4 b)
        {
            Fix64 dx = a.x - b.x;
            Fix64 dy = a.y - b.y;
            Fix64 dz = a.z - b.z;
            Fix64 dw = a.w - b.w;

            return dx * dx + dy * dy + dz * dz + dw * dw;
        }

        public static Fix64 Dot(FVector4 a, FVector4 b)
        {
            return a.x * b.x + a.y * b.y + a.z * b.z + a.w * b.w;
        }

        public static FVector4 Scale(FVector4 a, FVector4 b)
        {
            return new FVector4(a.x * b.x, a.y * b.y, a.z * b.z, a.w * b.w);
        }

        public static FVector4 Lerp(FVector4 a, FVector4 b, Fix64 t)
        {
            return new FVector4(
                Fix64.Lerp(a.x, b.x, t),
                Fix64.Lerp(a.y, b.y, t),
                Fix64.Lerp(a.z, b.z, t),
                Fix64.Lerp(a.w, b.w, t));
        }

        public static FVector4 LerpClamped(FVector4 a, FVector4 b, Fix64 t)
        {
            if (t > Fix64.one)
            {
                t = Fix64.one;
            }
            else if (t < Fix64.zero)
            {
                t = Fix64.zero;
            }

            return Lerp(a, b, t);
        }

        public static FVector4 Max(FVector4 a, FVector4 b)
        {
            return new FVector4(Fix64.Max(a.x, b.x), Fix64.Max(a.y, b.y), Fix64.Max(a.z, b.z), Fix64.Max(a.w, b.w));
        }

        public static FVector4 Min(FVector4 a, FVector4 b)
        {
            return new FVector4(Fix64.Min(a.x, b.x), Fix64.Min(a.y, b.y), Fix64.Min(a.z, b.z), Fix64.Min(a.w, b.w));
        }
    }
}
