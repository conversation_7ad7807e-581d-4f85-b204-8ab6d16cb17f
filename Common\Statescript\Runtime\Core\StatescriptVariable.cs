using System;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Variable types supported by the Statescript system
    /// </summary>
    public enum StatescriptVariableType
    {
        <PERSON><PERSON><PERSON>,
        Integer,
        Float,
        String,
        Vector2,
        Vector3,
        Entity,
        Object
    }

    /// <summary>
    /// Represents a variable in a Statescript graph
    /// </summary>
    [MemoryPackable]
    public sealed partial class StatescriptVariable
    {
        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public StatescriptVariableType Type { get; set; }
        
        [MemoryPackInclude]
        public object Value { get; set; }
        
        [MemoryPackInclude]
        public object DefaultValue { get; set; }
        
        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public bool IsExposed { get; set; } = false;

        public StatescriptVariable()
        {
        }

        public StatescriptVariable(string name, StatescriptVariableType type, object defaultValue = null)
        {
            Name = name;
            Type = type;
            DefaultValue = defaultValue;
            Value = defaultValue;
        }

        /// <summary>
        /// Get the value as a specific type
        /// </summary>
        public T GetValue<T>(T fallback = default)
        {
            if (Value is T typedValue)
            {
                return typedValue;
            }
            
            // Try to convert if possible
            try
            {
                if (Value != null && typeof(T) != typeof(object))
                {
                    return (T)Convert.ChangeType(Value, typeof(T));
                }
            }
            catch
            {
                // Conversion failed, return fallback
            }
            
            return fallback;
        }

        /// <summary>
        /// Set the value with type checking
        /// </summary>
        public bool SetValue(object newValue)
        {
            if (IsValidValueForType(newValue, Type))
            {
                Value = newValue;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Reset to default value
        /// </summary>
        public void ResetToDefault()
        {
            Value = DefaultValue;
        }

        /// <summary>
        /// Check if a value is valid for the specified type
        /// </summary>
        public static bool IsValidValueForType(object value, StatescriptVariableType type)
        {
            if (value == null) return true; // Null is valid for all types

            return type switch
            {
                StatescriptVariableType.Boolean => value is bool,
                StatescriptVariableType.Integer => value is int or long or short or byte,
                StatescriptVariableType.Float => value is float or double or decimal,
                StatescriptVariableType.String => value is string,
                StatescriptVariableType.Vector2 => value is FVector2,
                StatescriptVariableType.Vector3 => value is FVector3,
                StatescriptVariableType.Entity => value is Logic.Entity,
                StatescriptVariableType.Object => true, // Object can be anything
                _ => false
            };
        }

        /// <summary>
        /// Get the default value for a variable type
        /// </summary>
        public static object GetDefaultValueForType(StatescriptVariableType type)
        {
            return type switch
            {
                StatescriptVariableType.Boolean => false,
                StatescriptVariableType.Integer => 0,
                StatescriptVariableType.Float => 0.0f,
                StatescriptVariableType.String => string.Empty,
                StatescriptVariableType.Vector2 => FVector2.zero,
                StatescriptVariableType.Vector3 => FVector3.zero,
                StatescriptVariableType.Entity => null,
                StatescriptVariableType.Object => null,
                _ => null
            };
        }

        public override string ToString()
        {
            return $"{Name} ({Type}): {Value}";
        }
    }

    /// <summary>
    /// Variable nodes for getting and setting variables
    /// </summary>
    [MemoryPackable]
    public sealed partial class GetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;

        public GetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Get Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                // Store the value in the node's output for other nodes to access
                SetProperty("OutputValue", variable.Value);
                CompleteExecution(true);
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Get Variable node '{Name}' must specify a variable name");
            }
            
            return errors;
        }
    }

    /// <summary>
    /// Node for setting variable values
    /// </summary>
    [MemoryPackable]
    public sealed partial class SetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public object NewValue { get; set; }

        public SetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Set Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                if (variable.SetValue(NewValue))
                {
                    CompleteExecution(true);
                }
                else
                {
                    Context?.LogError($"Failed to set variable '{VariableName}' - invalid value type");
                    CompleteExecution(false);
                }
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Set Variable node '{Name}' must specify a variable name");
            }
            
            return errors;
        }
    }
}
