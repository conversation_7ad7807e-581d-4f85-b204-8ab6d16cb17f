﻿using FixedMath;

namespace Common.Physics {

    /**
     *  @brief Represents physical properties of a {@link RigidBody}. 
     **/
    public class BodyMaterial {

        internal Fix64 kineticFriction = Fix64.one / 4;
        internal Fix64 staticFriction = Fix64.one / 2;
        internal Fix64 restitution = Fix64.zero;

        public BodyMaterial() { }

        /**
         *  @brief Elastic restituion. 
         **/
        public Fix64 Restitution {
            get { return restitution; }
            set { restitution = value; }
        }

        /**
         *  @brief Static friction. 
         **/
        public Fix64 StaticFriction {
            get { return staticFriction; }
            set { staticFriction = value; }
        }

        /**
         *  @brief Kinectic friction. 
         **/
        public Fix64 KineticFriction {
            get { return kineticFriction; }
            set { kineticFriction = value; }
        }

    }

}