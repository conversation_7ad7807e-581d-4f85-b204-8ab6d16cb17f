﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

using FixedMath;

namespace Common.Physics {
    /// <summary>
    /// The distance between two given points on two bodies will not
    /// exceed a value.
    /// </summary>
    public class PointPointDistance : Constraint
    {
        public enum DistanceBehavior
        {
            LimitDistance,
            LimitMaximumDistance,
            LimitMinimumDistance,
        }

        private FVector3 localAnchor1, localAnchor2;
        private FVector3 r1, r2;

        private Fix64 biasFactor = Fix64.EN1;
        private Fix64 softness = Fix64.EN2;
        private Fix64 distance;

        private DistanceBehavior behavior = DistanceBehavior.LimitDistance;

        /// <summary>
        /// Initializes a new instance of the DistanceConstraint class.
        /// </summary>
        /// <param name="body1">The first body.</param>
        /// <param name="body2">The second body.</param>
        /// <param name="anchor1">The anchor point of the first body in world space. 
        /// The distance is given by the initial distance between both anchor points.</param>
        /// <param name="anchor2">The anchor point of the second body in world space.
        /// The distance is given by the initial distance between both anchor points.</param>
        public PointPointDistance(RigidBody body1, RigidBody body2, FVector3 anchor1,FVector3 anchor2)
            : base(body1, body2)
        {
            FVector3.Subtract(ref anchor1, ref body1.position, out localAnchor1);
            FVector3.Subtract(ref anchor2, ref body2.position, out localAnchor2);

            FVector3.Transform(ref localAnchor1, ref body1.invOrientation, out localAnchor1);
            FVector3.Transform(ref localAnchor2, ref body2.invOrientation, out localAnchor2);

            distance = (anchor1 - anchor2).magnitude;
        }

        public Fix64 AppliedImpulse { get { return accumulatedImpulse; } }

        /// <summary>
        /// 
        /// </summary>
        public Fix64 Distance { get { return distance; } set { distance = value; } }

        /// <summary>
        /// 
        /// </summary>
        public DistanceBehavior Behavior { get { return behavior; } set { behavior = value; } }

        /// <summary>
        /// The anchor point of body1 in local (body) coordinates.
        /// </summary>
        public FVector3 LocalAnchor1 { get { return localAnchor1; } set { localAnchor1 = value; } }

        /// <summary>
        /// The anchor point of body2 in local (body) coordinates.
        /// </summary>
        public FVector3 LocalAnchor2 { get { return localAnchor2; } set { localAnchor2 = value; } }

        /// <summary>
        /// Defines how big the applied impulses can get.
        /// </summary>
        public Fix64 Softness { get { return softness; } set { softness = value; } }

        /// <summary>
        /// Defines how big the applied impulses can get which correct errors.
        /// </summary>
        public Fix64 BiasFactor { get { return biasFactor; } set { biasFactor = value; } }

        Fix64 effectiveMass = Fix64.zero;
        Fix64 accumulatedImpulse = Fix64.zero;
        Fix64 bias;
        Fix64 softnessOverDt;
        
        FVector3[] jacobian = new FVector3[4];

        bool skipConstraint = false;

        /// <summary>
        /// Called once before iteration starts.
        /// </summary>
        /// <param name="timestep">The 5simulation timestep</param>
        public override void PrepareForIteration(Fix64 timestep)
        {
            FVector3.Transform(ref localAnchor1, ref body1.orientation, out r1);
            FVector3.Transform(ref localAnchor2, ref body2.orientation, out r2);

            FVector3 p1, p2, dp;
            FVector3.Add(ref body1.position, ref r1, out p1);
            FVector3.Add(ref body2.position, ref r2, out p2);

            FVector3.Subtract(ref p2, ref p1, out dp);

            Fix64 deltaLength = dp.magnitude - distance;

            if (behavior == DistanceBehavior.LimitMaximumDistance && deltaLength <= Fix64.zero)
            {
                skipConstraint = true;
            }
            else if (behavior == DistanceBehavior.LimitMinimumDistance && deltaLength >= Fix64.zero)
            {
                skipConstraint = true;
            }
            else
            {
                skipConstraint = false;

                FVector3 n = p2 - p1;
                if (n.sqrMagnitude != Fix64.zero) n.Normalize();

                jacobian[0] = -Fix64.one * n;
                jacobian[1] = -Fix64.one * FVector3.Cross(r1, n);
                jacobian[2] = Fix64.one * n;
                jacobian[3] = FVector3.Cross(r2 , n);

                effectiveMass = body1.inverseMass + body2.inverseMass
                    + FVector3.Transform(jacobian[1], body1.invInertiaWorld) * jacobian[1]
                    + FVector3.Transform(jacobian[3], body2.invInertiaWorld) * jacobian[3];

                softnessOverDt = softness / timestep;
                effectiveMass += softnessOverDt;

                effectiveMass = Fix64.one / effectiveMass;

                bias = deltaLength * biasFactor * (Fix64.one / timestep);

                if (!body1.isStatic)
                {
                    body1.linearVelocity += body1.inverseMass * accumulatedImpulse * jacobian[0];
                    body1.angularVelocity += FVector3.Transform(accumulatedImpulse * jacobian[1], body1.invInertiaWorld);
                }

                if (!body2.isStatic)
                {
                    body2.linearVelocity += body2.inverseMass * accumulatedImpulse * jacobian[2];
                    body2.angularVelocity += FVector3.Transform(accumulatedImpulse * jacobian[3], body2.invInertiaWorld);
                }
            }
            
        }

        /// <summary>
        /// Iteratively solve this constraint.
        /// </summary>
        public override void Iterate()
        {
            if (skipConstraint) return;

            Fix64 jv =
                body1.linearVelocity * jacobian[0] +
                body1.angularVelocity * jacobian[1] +
                body2.linearVelocity * jacobian[2] +
                body2.angularVelocity * jacobian[3];

            Fix64 softnessScalar = accumulatedImpulse * softnessOverDt;

            Fix64 lambda = -effectiveMass * (jv + bias + softnessScalar);

            if (behavior == DistanceBehavior.LimitMinimumDistance)
            {
                Fix64 previousAccumulatedImpulse = accumulatedImpulse;
                accumulatedImpulse = Fix64.Max(accumulatedImpulse + lambda, 0);
                lambda = accumulatedImpulse - previousAccumulatedImpulse;
            }
            else if (behavior == DistanceBehavior.LimitMaximumDistance)
            {
                Fix64 previousAccumulatedImpulse = accumulatedImpulse;
                accumulatedImpulse = Fix64.Min(accumulatedImpulse + lambda, 0);
                lambda = accumulatedImpulse - previousAccumulatedImpulse;
            }
            else
            {
                accumulatedImpulse += lambda;
            }

            if (!body1.isStatic)
            {
                body1.linearVelocity += body1.inverseMass * lambda * jacobian[0];
                body1.angularVelocity += FVector3.Transform(lambda * jacobian[1], body1.invInertiaWorld);
            }

            if (!body2.isStatic)
            {
                body2.linearVelocity += body2.inverseMass * lambda * jacobian[2];
                body2.angularVelocity += FVector3.Transform(lambda * jacobian[3], body2.invInertiaWorld);
            }
        }


        public override void DebugDraw(IDebugDrawer drawer)
        {
            drawer.DrawLine(body1.position + r1, body2.position + r2);
        }

    }
}
