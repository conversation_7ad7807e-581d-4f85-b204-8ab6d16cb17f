<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net7.0;net8.0;netstandard2.1</TargetFrameworks>
        <!-- This project is meta package -->
        <IncludeBuildOutput>false</IncludeBuildOutput>
        <IncludeContentInPack>true</IncludeContentInPack>
        <NoWarn>$(NoWarn);NU5128</NoWarn>
        <PackageTags>serializer</PackageTags>
        <Description>Zero encoding extreme performance binary serializer for C#.</Description>
    </PropertyGroup>

    <ItemGroup>
        <EmbeddedResource Include="..\..\LICENSE.md" />
        <None Include="../../Icon.png" Pack="true" PackagePath="/" />
        <ProjectReference Include="..\MemoryPack.Core\MemoryPack.Core.csproj" />
        <ProjectReference Include="..\MemoryPack.Generator\MemoryPack.Generator.csproj" />
    </ItemGroup>

</Project>
