﻿using System;
using UnityEngine;

namespace FixedMath
{
    /// <summary>
    /// 定点数类型
    /// 1.定点数(与数值类型的相互转换int<->fixed,float<->fixed),加减乘除运算(+、-、*、\)
    /// 2.位布局：第63位为符号位(1位)，第47-16位为整数位(32位)，第15-0位为小数位（16位）
    /// 3.小数精度：1/65536 = 0.0000152587890625
    /// 4.可以考虑的优化方式，提供一组针对rawvalue的计算函数，用户可以保存中间值，用于后续的计算。
    /// </summary>
    [Serializable]
    public partial struct Fix64
    {

        public const int fractionBits = 16;
        public const long fractionFactor = ((long)1 << fractionBits);

        static Fix64 _zero = FromInt(0);
        static Fix64 _one = FromInt(1);
        static Fix64 _maxValue = FromRawValue(0x00007fffffffffff);
        static Fix64 _minValue = FromRawValue(-_maxValue.rawValue);

        static Fix64 _e = FromRawValue(0x000000000002b7e1);//FromDouble(2.7182818284590451);
        static Fix64 _pi = FromRawValue(0x000000000003243f);//FromDouble(3.1415926535897931);
        static Fix64 _piOver2 = FromRawValue(0x000000000001921f);
        static Fix64 _positiveInfinity = FromRawValue(long.MaxValue);
        static Fix64 _negativeInfinity = FromRawValue(long.MinValue);
        static Fix64 _epsilon = FromRawValue(1);

        static Fix64 _degToRad = FromRawValue(0x0000000000000477);
        static Fix64 _radToDeg = FromRawValue(0x0000000000394bbf);


        public static Fix64 zero { get { return _zero; } }
        public static Fix64 one { get { return _one; } }
        public static Fix64 maxValue { get { return _maxValue; } }
        public static Fix64 minValue { get { return _minValue; } }
        public static Fix64 e { get { return _e; } }
        public static Fix64 pi { get { return _pi; } }

        public static Fix64 piOver2 { get { return _piOver2; } }
        public static Fix64 epsilon { get { return _epsilon; } }
        public static Fix64 positiveInfinity { get { return _positiveInfinity; } }
        public static Fix64 negativeInfinity { get { return _negativeInfinity; } }


        public const long HALF = 1L << (fractionBits - 1);
        public static Fix64 half { get { return Fix64.FromRawValue(HALF); } }

        public static readonly Fix64 EN1 = Fix64.one / 10;
        public static readonly Fix64 EN2 = Fix64.one / 100;
        public static readonly Fix64 EN3 = Fix64.one / 1000;
        public static readonly Fix64 EN4 = Fix64.one / 10000;
        public static readonly Fix64 EN5 = Fix64.one / 100000;
        public static readonly Fix64 EN6 = Fix64.one / 1000000;
        public static readonly Fix64 EN7 = Fix64.one / 10000000;
        public static readonly Fix64 EN8 = Fix64.one / 100000000;



        public static Fix64 DegToRad(Fix64 angle)
        {
            return angle / _radToDeg;
        }

        public static Fix64 RadToDeg(Fix64 angle)
        {
            return angle / _degToRad;
        }



        public long rawValue { get { return mRawValue; } }

        [SerializeField]
        [Range(-0x00007fffffffffff, 0x00007fffffffffff)]
        private long mRawValue;

        public bool isInfinity
        {
            get
            {
                return mRawValue == _positiveInfinity.mRawValue || 
                       mRawValue == _negativeInfinity.mRawValue;
            }
        }

        public bool isPositiveInfinity
        {
            get
            {
                return mRawValue == _positiveInfinity.mRawValue;
            }
        }

        public bool isNegativeInfinity
        {
            get
            {
                return mRawValue == _negativeInfinity.mRawValue;
            }
        }

        public static Fix64 FromInt(int val)
        {
            return new Fix64(val);
        }

        public static Fix64 FromSingle(float val)
        {
            return new Fix64(val);
        }


        public static Fix64 FromDouble(double val)
        {
            return new Fix64(val);
        }


        public double ToDouble()
        {
            return (integer + fraction / (double)(fractionFactor));
        }

        public float ToSingle()
        {
            return (integer + fraction / (float)(fractionFactor));
        }


        public Fix64(int val)
        {
            mRawValue = val * fractionFactor;
        }

        public static Fix64 FromRawValue(long rawValue)
        {
            Fix64 val = new Fix64();
            val.mRawValue = rawValue;

            return val;
        }

        public static Fix64 FromComponents(long i, long f)
        {
            return FromRawValue(i * fractionFactor + f);
        }

        public long integer
        {
            get { return rawValue / fractionFactor; }
        }

        public long fraction
        {
            get
            {
                return rawValue % fractionFactor;
            }
        }

        public void GetIntegerAndFraction(out long integer, out long fraction)
        {
            fraction = rawValue % fractionFactor;
            integer = (rawValue - fraction) >> fractionBits;
        }

        public Fix64(double val)
        {
            mRawValue = (long)(val * fractionFactor);
        }

        public static Fix64 operator -(Fix64 a)
        {
            return FromRawValue(-a.mRawValue);
        }

        public static Fix64 operator +(Fix64 a, Fix64 b)
        {
            return FromRawValue(a.mRawValue + b.mRawValue);
        }

        public static Fix64 operator -(Fix64 a, Fix64 b)
        {
            return FromRawValue(a.mRawValue - b.mRawValue);
        }

        public static bool operator ==(Fix64 a, Fix64 b)
        {
            return a.mRawValue == b.mRawValue;
        }

        public static bool operator !=(Fix64 a, Fix64 b)
        {
            return a.mRawValue != b.mRawValue;
        }

        public static bool operator >(Fix64 a, Fix64 b)
        {
            return a.mRawValue > b.mRawValue;
        }

        public static bool operator >=(Fix64 a, Fix64 b)
        {
            return a.mRawValue >= b.mRawValue;
        }

        public static bool operator <(Fix64 a, Fix64 b)
        {
            return a.mRawValue < b.mRawValue;
        }

        public static bool operator <=(Fix64 a, Fix64 b)
        {
            return a.mRawValue <= b.mRawValue;
        }

        public override bool Equals(object obj)
        {
            return mRawValue == ((Fix64)obj).mRawValue;
        }

        public static Fix64 operator *(Fix64 a, Fix64 b)
        {
            long f1 = a.mRawValue % fractionFactor;   //小数部分
            long f2 = b.mRawValue % fractionFactor;

            long i1 = (a.mRawValue - f1) >> fractionBits;   //整数部分
            long i2 = (b.mRawValue - f2) >> fractionBits;


            //(i1 + f1) * (i2 + f2) = i1*i2 + i1*f2 + i2*f1 + f1*f2
            long i1xi2 = (i1 * i2) << fractionBits;
            long i1xf2 = i1 * f2 ;
            long i2xf1 = i2 * f1;
            long f1xf2 = (f1 * f2) >> fractionBits;

            var sum = (long)i1xi2 + i1xf2 + i2xf1 + f1xf2;
            //long r1 = ((a.mRawValue * i2)) + i1 * f2 + ((f1 * f2) / fractionFactor);

            return FromRawValue(sum);
        }

        public static Fix64 operator *(Fix64 a, int b)
        {
            return FromRawValue(a.mRawValue * b);
        }

        public static Fix64 operator *(int a, Fix64 b)
        {
            return FromRawValue(a * b.mRawValue);
        }

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long Mul(long i1, long f1, long i2, long f2)
        //{
        //    return ((i1 * i2) << fractionBits) + i1 * f2 + i2 * f1 + ((f1 * f2) / fractionFactor);
        //}

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long Mul(long a, long b)
        //{
        //    long f1 = a % fractionFactor; 
        //    long f2 = b % fractionFactor;
        //    long i1 = (a - f1) >> fractionBits; 
        //    long i2 = (b - f2) >> fractionBits;

        //    return ((a * i2)) + i1 * f2 + ((f1 * f2) / fractionFactor);
        //}

        /// <summary>
        /// 用于特殊优化计算.
        /// 用户可以自行保存小数部分和整数部分，以此不用每次都计算小数和整数。
        /// </summary>
        /// <param name="i1">第1个操作数的整数部分</param>
        /// <param name="f1">第1个操作数的小数部分</param>
        /// <param name="i2">第2个操作数的整数部分</param>
        /// <param name="f2">第2个操作数的小数部分</param>
        /// <returns></returns>
        //public static long MulInteger(long a, int b)
        //{
        //    return a * b;
        //}

        public static Fix64 operator /(Fix64 a, Fix64 b)
        {
            if (b.mRawValue == 0)
            {
                Debug.LogError("Div Zero Error!");
                return Fix64.maxValue;
                b.mRawValue = 1;
            }

            return FromRawValue((a.mRawValue << fractionBits) / b.mRawValue);
        }

        public static Fix64 operator /(Fix64 a, int b)
        {
            if(b == 0)
            {
                Debug.LogError("Div Zero Error2!");
                return Fix64.maxValue;
            }
            return FromRawValue(a.mRawValue / b);
        }

        #region implicit convert
        public static implicit operator Fix64(double v)
        {
            return Fix64.FromDouble(v);
        }

        public static implicit operator Fix64(int n)
        {
            return Fix64.FromInt(n);
        }

        public static implicit operator Fix64(long n)
        {
            return Fix64.FromInt((int)n);
        }

        //public static implicit operator long(Fix64 f)
        //{
        //    return f.mRawValue >> fractionBits;
        //}

        public static implicit operator float(Fix64 f)
        {
            return f.ToSingle();
        }
        #endregion implicit convert

        public override string ToString()
        {
            return string.Format("{0:F8}", (integer + fraction / (double)(fractionFactor)));
        }

        public override int GetHashCode()
        {
            return (int)mRawValue;
        }

        public static Fix64 Max(Fix64 a, Fix64 b)
        {
            return a >= b ? a : b;
        }

        public static Fix64 Min(Fix64 a, Fix64 b)
        {
            return a <= b ? a : b;
        }

        public static Fix64 Lerp(Fix64 a, Fix64 b, Fix64 t)
        {
            return a + (b - a) * t;
        }

        public static int Sign(Fix64 f)
        {
            return f.Sign();
        }

        public int Sign()
        {
            return Sign(mRawValue);
        }

        static int Sign(long rawValue)
        {
            if (rawValue == 0) return 0;
            else if (rawValue < 0) return -1;
            else return 1;

            //实现这么复杂干嘛？？？
            //uint k = (uint)(((ulong)(rawValue)) >> 63);
            //k ^= 1;

            //return (int)(k << 1) - 1;
        }

        public static Fix64 Abs(ref Fix64 f)
        {
            return f.Abs();
        }

        public Fix64 Abs()
        {
            return FromRawValue(_Abs(mRawValue));
        }

        private static long _Abs(long rawValue)
        {
            return System.Math.Abs(rawValue);
        }

        public static Fix64 Sqrt(Fix64 f)
        {
            return f.Sqrt();
        }

        public Fix64 Sqrt()
        {
            return FromRawValue(Sqrt(this.rawValue));
        }


        /// <summary>
        /// 1/sqrt(x)
        /// </summary>
        /// <returns></returns>
        public Fix64 InvSqrt()
        {
            return one / Sqrt();
        }

        /// <summary>
        /// 64位定点数开平方——用牛顿迭代法实现.
        /// </summary>
        /// <param name="rawValue"></param>
        /// <returns></returns>
        /// <remarks>
        /// sample_count = 1000000
        /// TestNewtonSqrt_Pref elapsedTime = 106471
        /// TestSqrt_Pref elapsedTime = 8140
        /// TestNewtonSqrtLong_Pref elapsedTime = 231810
        /// TestFastSqrt_Pref elapsedTime = 50450
        /// 
        /// 结论：
        ///  1.系统库sqrt性能是NewtonSqrtLong的25倍左右
        ///  2.系统库sqrt性能是fastsqrt的5倍  
        ///  3.浮点数由于其存储结构，可以迅速找到数量级上的平方数.
        ///  
        /// double / double
        /// 00FB4414  movsd       xmm0,mmword ptr [eax+0FBE168h]  
        /// 00FB441C  divsd       xmm0, mmword ptr[ecx + 0FBE168h]
        /// long / long
        /// 00FB44B1  mov         ecx, dword ptr samples(0FBE158h)[ecx]
        /// 00FB44B7  mov         eax, dword ptr samples(0FBE158h)[eax]
        /// 00FB44BD  cdq
        /// 00FB44BE  idiv        eax, ecx
        /// 00FB44C0  cdq

        /// int64/int64
        /// 000544CD  call        __alldiv (051352h)  
        /// </remarks>
        static long Sqrt(long rawValue)
        {
            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue << fractionBits;
                long res = rawValue >> 1;      // 初始值取x/2,可以提升20-25%左右的性能（rawValue为正数）
                long diff = 0;

                if (res != 0)
                {
                    do
                    {
                        last_res = res;
                        res = (res + y / res) >> 1;
                        diff = last_res - res;
                    }
                    while (diff > 1 || diff < -1);  //Xn+1 ~= Xn时，认为已经收敛
                }

                return res;
            }
            else if(rawValue == 0)
            {
                return 0;
            }
            else
            {
                throw new ArgumentOutOfRangeException("Negative value passed to Sqrt", "x");
            }
        }

        /// <summary>
        /// 指定初始进行运算
        /// </summary>
        /// <param name="rawValue"></param>
        /// <param name="x0"></param>
        /// <returns></returns>
        static long Sqrt(long rawValue, long x0)
        {
            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue << fractionBits;
                long res = x0;      // 初始值为外部指定的数值   
                long diff = 0;

                do
                {
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 增加一个counter，用于记录迭代的次数，该函数一般用于性能测试。
        /// sqrt性能优化的方向:
        /// 用最小的成本确定初始值，使得初始值距离真实集尽量接近,
        /// Quake3的做法是利用了浮点数表示法中，指数除2，使得初始解与真实解在数量级上一致，
        /// 后续再做一次牛顿迭代法，即可得到相对精确的解，如果需要增大精度，只需要多迭代几次。
        /// </summary>
        /// <param name="rawValue"></param>
        /// <param name="counter"></param>
        /// <returns></returns>
        public static long Sqrt(long rawValue, out int counter)
        {
            counter = 0;

            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue << fractionBits;
                long res = y >> 1;      // 初始值取x/2,可以提升20-25%左右的性能（rawValue为正数）
                long diff = 0;

                do
                {
                    ++counter;
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res;
            }
            else
            {
                return 0;
            }
        }

        public static long Sqrt(long rawValue, long x0, out int counter)
        {
            counter = 0;

            if (rawValue > 0)
            {
                long last_res = 0;
                long y = rawValue << fractionBits;
                long res = x0;      // 初始值为外部指定的数值   
                long diff = 0;

                do
                {
                    ++counter;
                    last_res = res;
                    res = (res + y / res) >> 1;
                    diff = last_res - res;
                }
                while (diff > 1 || diff < -1);

                return res;
            }
            else
            {
                return 0;
            }
        }

        public static Fix64 Repeat(Fix64 t, Fix64 length)
        {
            return t - length * (t / length).Floor();
        }


        public static Fix64 Ceiling(Fix64 value)
        {
            var hasFractionalPart = (value.rawValue & 0x000000000000FFFF) != 0;
            return hasFractionalPart ? Floor(value) + one : value;
        }

        public static int Floor(Fix64 f)
        {
            return f.Floor();
        }

        public int Floor()
        {
            // positive
            if (mRawValue >= 0)
            {
                return (int)integer;
            }
            else // negative
            {
                long i, f;
                GetIntegerAndFraction(out i, out f);

                if (f != 0) // f < 0
                {
                    return (int)(i - 1);
                }
                else
                {
                    return (int)i;
                }
            }
        }
    }
}
