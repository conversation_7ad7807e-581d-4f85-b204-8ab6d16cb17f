﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

#region Using Statements
using FixedMath;
using System;
#endregion

namespace Common.Physics {

    /// <summary>
    /// GJK based implementation of Raycasting.
    /// </summary>
    public sealed class GJKCollide {
        private static Fix64 CollideEpsilon = Fix64.EN3;
        private const int MaxIterations = 15;

        private static ResourcePool<VoronoiSimplexSolver> simplexSolverPool = new ResourcePool<VoronoiSimplexSolver>();

        #region private static void SupportMapTransformed(ISupportMappable support, ref JMatrix orientation, ref JVector position, ref JVector direction, out JVector result)
        private static void SupportMapTransformed(ISupportMappable support, ref FMatrix3x3 orientation, ref FVector3 position, ref FVector3 direction, out FVector3 result) {
            //JVector.Transform(ref direction, ref invOrientation, out result);
            //support.SupportMapping(ref result, out result);
            //JVector.Transform(ref result, ref orientation, out result);
            //JVector.Add(ref result, ref position, out result);

            //tranform to local space
            result.x = ((direction.x * orientation.m00) + (direction.y * orientation.m01)) + (direction.z * orientation.m02); 
            result.y = ((direction.x * orientation.m10) + (direction.y * orientation.m11)) + (direction.z * orientation.m12);
            result.z = ((direction.x * orientation.m20) + (direction.y * orientation.m21)) + (direction.z * orientation.m22);

            support.SupportMapping(ref result, out result);

            Fix64 x = ((result.x * orientation.m00) + (result.y * orientation.m10)) + (result.z * orientation.m20);
            Fix64 y = ((result.x * orientation.m01) + (result.y * orientation.m11)) + (result.z * orientation.m21);
            Fix64 z = ((result.x * orientation.m02) + (result.y * orientation.m12)) + (result.z * orientation.m22);

            result.x = position.x + x;
            result.y = position.y + y;
            result.z = position.z + z;
        }
        #endregion

        /// <summary>
        /// Checks if given point is within a shape.
        /// </summary>
        /// <param name="support">The supportmap implementation representing the shape.</param>
        /// <param name="orientation">The orientation of the shape.</param>
        /// <param name="invOrientation">The inverse orientation of the shape.</param>
        /// <param name="position">The position of the shape.</param>
        /// <param name="point">The point to check.</param>
        /// <returns>Returns true if the point is within the shape, otherwise false.</returns>
        public static bool Pointcast(ISupportMappable support, ref FMatrix3x3 orientation, ref FVector3 position, ref FVector3 point) {
            FVector3 arbitraryPoint;

            SupportMapTransformed(support, ref orientation, ref position, ref point, out arbitraryPoint);
            FVector3.Subtract(ref point, ref arbitraryPoint, out arbitraryPoint);

            FVector3 r; support.SupportCenter(out r);
            FVector3.Transform(ref r, ref orientation, out r);
            FVector3.Add(ref position, ref r, out r);
            FVector3.Subtract(ref point, ref r, out r);

            FVector3 x = point;
            FVector3 w, p;
            Fix64 VdotR;

            FVector3 v; FVector3.Subtract(ref x, ref arbitraryPoint, out v);
            Fix64 dist = v.sqrMagnitude;
            Fix64 epsilon = CollideEpsilon;

            int maxIter = MaxIterations;

            VoronoiSimplexSolver simplexSolver = simplexSolverPool.GetNew();

            simplexSolver.Reset();

            while ((dist > epsilon) && (maxIter-- != 0)) {
                SupportMapTransformed(support, ref orientation, ref position, ref v, out p);
                FVector3.Subtract(ref x, ref p, out w);

                Fix64 VdotW = FVector3.Dot(ref v, ref w);

                if (VdotW > Fix64.zero) {
                    VdotR = FVector3.Dot(ref v, ref r);

                    if (VdotR >= -(DMath.Epsilon * DMath.Epsilon)) { simplexSolverPool.GiveBack(simplexSolver); return false; } else simplexSolver.Reset();
                }
                if (!simplexSolver.InSimplex(w)) simplexSolver.AddVertex(w, x, p);

                if (simplexSolver.Closest(out v)) dist = v.sqrMagnitude;
                else dist = Fix64.zero;
            }

            simplexSolverPool.GiveBack(simplexSolver);
            return true;

        }


        public static bool ClosestPoints(ISupportMappable support1, ISupportMappable support2, ref FMatrix3x3 orientation1,
            ref FMatrix3x3 orientation2, ref FVector3 position1, ref FVector3 position2,
            out FVector3 p1, out FVector3 p2, out FVector3 normal) {

            VoronoiSimplexSolver simplexSolver = simplexSolverPool.GetNew();
            simplexSolver.Reset();

            p1 = p2 = FVector3.zero;

            FVector3 r = position1 - position2;
            FVector3 w, v;

            FVector3 supVertexA;
            FVector3 rn, vn;

            rn = FVector3.Negate(r);

            SupportMapTransformed(support1, ref orientation1, ref position1, ref rn, out supVertexA);

            FVector3 supVertexB;
            SupportMapTransformed(support2, ref orientation2, ref position2, ref r, out supVertexB);

            v = supVertexA - supVertexB;

            normal = FVector3.zero;

            int maxIter = MaxIterations;

            Fix64 distSq = v.sqrMagnitude;
            Fix64 epsilon = CollideEpsilon;

            while ((distSq > epsilon) && (maxIter-- != 0)) {
                vn = FVector3.Negate(v);
                SupportMapTransformed(support1, ref orientation1, ref position1, ref vn, out supVertexA);
                SupportMapTransformed(support2, ref orientation2, ref position2, ref v, out supVertexB);
                w = supVertexA - supVertexB;

                if (!simplexSolver.InSimplex(w)) simplexSolver.AddVertex(w, supVertexA, supVertexB);
                if (simplexSolver.Closest(out v)) {
                    distSq = v.sqrMagnitude;
                    normal = v;
                } else distSq = Fix64.zero;
            }


            simplexSolver.ComputePoints(out p1, out p2);

            if (normal.sqrMagnitude > DMath.Epsilon * DMath.Epsilon)
                normal.Normalize();

            simplexSolverPool.GiveBack(simplexSolver);

            return true;

        }

        #region TimeOfImpact Conservative Advancement - Depricated
        //    public static bool TimeOfImpact(ISupportMappable support1, ISupportMappable support2, ref JMatrix orientation1,
        //ref JMatrix orientation2, ref JVector position1, ref JVector position2, ref JVector sweptA, ref JVector sweptB,
        //out JVector p1, out JVector p2, out JVector normal)
        //    {

        //        VoronoiSimplexSolver simplexSolver = simplexSolverPool.GetNew();
        //        simplexSolver.Reset();

        //        Fix64 lambda = Fix64.zero;

        //        p1 = p2 = JVector.Zero;

        //        JVector x1 = position1;
        //        JVector x2 = position2;

        //        JVector r = sweptA - sweptB;
        //        JVector w, v;

        //        JVector supVertexA;
        //        JVector rn = JVector.Negate(r);
        //        SupportMapTransformed(support1, ref orientation1, ref x1, ref rn, out supVertexA);

        //        JVector supVertexB;
        //        SupportMapTransformed(support2, ref orientation2, ref x2, ref r, out supVertexB);

        //        v = supVertexA - supVertexB;

        //        bool hasResult = false;

        //        normal = JVector.Zero;


        //        Fix64 lastLambda = lambda;

        //        int maxIter = MaxIterations;

        //        Fix64 distSq = v.LengthSquared();
        //        Fix64 epsilon = Fix64.EN5;

        //        Fix64 VdotR;

        //        while ((distSq > epsilon) && (maxIter-- != 0))
        //        {

        //            JVector vn = JVector.Negate(v);
        //            SupportMapTransformed(support1, ref orientation1, ref x1, ref vn, out supVertexA);
        //            SupportMapTransformed(support2, ref orientation2, ref x2, ref v, out supVertexB);
        //            w = supVertexA - supVertexB;

        //            Fix64 VdotW = JVector.Dot(ref v, ref w);

        //            if (VdotW > Fix64.zero)
        //            {
        //                VdotR = JVector.Dot(ref v, ref r);

        //                if (VdotR >= -JMath.Epsilon)
        //                {
        //                    simplexSolverPool.GiveBack(simplexSolver);
        //                    return false;
        //                }
        //                else
        //                {
        //                    lambda = lambda - VdotW / VdotR;


        //                    x1 = position1 + lambda * sweptA;
        //                    x2 = position2 + lambda * sweptB;

        //                    w = supVertexA - supVertexB;

        //                    normal = v;
        //                    hasResult = true;
        //                }
        //            }
        //            if (!simplexSolver.InSimplex(w)) simplexSolver.AddVertex(w, supVertexA, supVertexB);
        //            if (simplexSolver.Closest(out v))
        //            {
        //                distSq = v.LengthSquared();
        //                normal = v;
        //                hasResult = true;
        //            }
        //            else distSq = Fix64.zero;
        //        }


        //        simplexSolver.ComputePoints(out p1, out p2);


        //        if (normal.LengthSquared() > JMath.Epsilon * JMath.Epsilon)
        //            normal.Normalize();

        //        p1 = p1 - lambda * sweptA;
        //        p2 = p2 - lambda * sweptB;

        //        simplexSolverPool.GiveBack(simplexSolver);

        //        return true;

        //    }
        #endregion

        // see: btSubSimplexConvexCast.cpp

        /// <summary>
        /// Checks if a ray definied through it's origin and direction collides
        /// with a shape.
        /// </summary>
        /// <param name="support">The supportmap implementation representing the shape.</param>
        /// <param name="orientation">The orientation of the shape.</param>
        /// <param name="invOrientation">The inverse orientation of the shape.</param>
        /// <param name="position">The position of the shape.</param>
        /// <param name="origin">The origin of the ray.</param>
        /// <param name="direction">The direction of the ray.</param>
        /// <param name="fraction">The fraction which gives information where at the 
        /// ray the collision occured. The hitPoint is calculated by: origin+fraction*direction.</param>
        /// <param name="normal">The normal from the ray collision.</param>
        /// <returns>Returns true if the ray hit the shape, false otherwise.</returns>
        public static bool Raycast(ISupportMappable support, ref FMatrix3x3 orientation, ref FMatrix3x3 invOrientation,
            ref FVector3 position, ref FVector3 origin, ref FVector3 direction, out Fix64 fraction, out FVector3 normal) 
        {
            VoronoiSimplexSolver simplexSolver = simplexSolverPool.GetNew();
            simplexSolver.Reset();

            normal = FVector3.zero;
            fraction = Fix64.maxValue;

            Fix64 lambda = Fix64.zero;

            FVector3 r = direction;
            FVector3 x = origin;
            FVector3 w, p, v;

            FVector3 arbitraryPoint;
            SupportMapTransformed(support, ref orientation, ref position, ref r, out arbitraryPoint);
            FVector3.Subtract(ref x, ref arbitraryPoint, out v);

            int maxIter = MaxIterations;

            Fix64 distSq = v.sqrMagnitude;
            Fix64 epsilon = Fix64.EN4;

            Fix64 VdotR;

            while ((distSq > epsilon) && (maxIter-- != 0)) {
                SupportMapTransformed(support, ref orientation, ref position, ref v, out p);
                FVector3.Subtract(ref x, ref p, out w);

                Fix64 VdotW = FVector3.Dot(ref v, ref w);

                if (VdotW > Fix64.zero) {
                    VdotR = FVector3.Dot(ref v, ref r);

                    if (VdotR >= -DMath.Epsilon) {
                        simplexSolverPool.GiveBack(simplexSolver);
                        return false;
                    } else {
                        lambda = lambda - VdotW / VdotR;
                        FVector3.Multiply(ref r, lambda, out x);
                        FVector3.Add(ref origin, ref x, out x);
                        FVector3.Subtract(ref x, ref p, out w);
                        normal = v;
                    }
                }
                if (!simplexSolver.InSimplex(w)) simplexSolver.AddVertex(w, x, p);
                if (simplexSolver.Closest(out v)) { distSq = v.sqrMagnitude; } else distSq = Fix64.zero;
            }

            #region Retrieving hitPoint

            // Giving back the fraction like this *should* work
            // but is inaccurate against large objects:
            // fraction = lambda;

            FVector3 p1, p2;
            simplexSolver.ComputePoints(out p1, out p2);

            p2 = p2 - origin;
            fraction = p2.magnitude / direction.magnitude;

            #endregion

            if (normal.sqrMagnitude > DMath.Epsilon * DMath.Epsilon)
                normal.Normalize();

            simplexSolverPool.GiveBack(simplexSolver);

            return true;
        }

        // see: btVoronoiSimplexSolver.cpp
        #region private class VoronoiSimplexSolver - Bullet

        // Bullet has problems with raycasting large objects - so does jitter
        // hope to fix that in the next versions.

        /*
          Bullet for XNA Copyright (c) 2003-2007 Vsevolod Klementjev http://www.codeplex.com/xnadevru
          Bullet original C++ version Copyright (c) 2003-2007 Erwin Coumans http://bulletphysics.com

          This software is provided 'as-is', without any express or implied
          warranty.  In no event will the authors be held liable for any damages
          arising from the use of this software.

          Permission is granted to anyone to use this software for any purpose,
          including commercial applications, and to alter it and redistribute it
          freely, subject to the following restrictions:

          1. The origin of this software must not be misrepresented; you must not
             claim that you wrote the original software. If you use this software
             in a product, an acknowledgment in the product documentation would be
             appreciated but is not required.
          2. Altered source versions must be plainly marked as such, and must not be
             misrepresented as being the original software.
          3. This notice may not be removed or altered from any source distribution.
        */

    }

    /// VoronoiSimplexSolver is an implementation of the closest point distance
    /// algorithm from a 1-4 points simplex to the origin.
    /// Can be used with GJK, as an alternative to Johnson distance algorithm. 
    public class VoronoiSimplexSolver {
        private const int VertexA = 0, VertexB = 1, VertexC = 2, VertexD = 3;

        private const int VoronoiSimplexMaxVerts = 5;
        private const bool CatchDegenerateTetrahedron = true;

        private int _numVertices;

        private FVector3[] _simplexVectorW = new FVector3[VoronoiSimplexMaxVerts];
        private FVector3[] _simplexPointsP = new FVector3[VoronoiSimplexMaxVerts];
        private FVector3[] _simplexPointsQ = new FVector3[VoronoiSimplexMaxVerts];

        private FVector3 _cachedPA;
        private FVector3 _cachedPB;
        private FVector3 _cachedV;
        private FVector3 _lastW;
        private bool _cachedValidClosest;

        private SubSimplexClosestResult _cachedBC = new SubSimplexClosestResult();

        // Note that this assumes ray-casts and point-casts will always be called from the
        // same thread which I assume is true from the _cachedBC member
        // If this needs to made multi-threaded a resource pool will be needed
        private SubSimplexClosestResult tempResult = new SubSimplexClosestResult();

        private bool _needsUpdate;

        #region ISimplexSolver Members

        public bool FullSimplex {
            get {
                return _numVertices == 4;
            }
        }

        public int NumVertices {
            get {
                return _numVertices;
            }
        }

        public void Reset() {
            _cachedValidClosest = false;
            _numVertices = 0;
            _needsUpdate = true;
            _lastW = new FVector3(Fix64.maxValue, Fix64.maxValue, Fix64.maxValue);
            _cachedBC.Reset();
        }

        public void AddVertex(FVector3 w, FVector3 p, FVector3 q) {
            _lastW = w;
            _needsUpdate = true;

            _simplexVectorW[_numVertices] = w;
            _simplexPointsP[_numVertices] = p;
            _simplexPointsQ[_numVertices] = q;

            _numVertices++;
        }

        //return/calculate the closest vertex
        public bool Closest(out FVector3 v) {
            bool succes = UpdateClosestVectorAndPoints();
            v = _cachedV;
            return succes;
        }

        public Fix64 MaxVertex {
            get {
                int numverts = NumVertices;
                Fix64 maxV = Fix64.zero, curLen2;
                for (int i = 0; i < numverts; i++) {
                    curLen2 = _simplexVectorW[i].sqrMagnitude;
                    if (maxV < curLen2) maxV = curLen2;
                }
                return maxV;
            }
        }

        //return the current simplex
        public int GetSimplex(out FVector3[] pBuf, out FVector3[] qBuf, out FVector3[] yBuf) {
            int numverts = NumVertices;
            pBuf = new FVector3[numverts];
            qBuf = new FVector3[numverts];
            yBuf = new FVector3[numverts];
            for (int i = 0; i < numverts; i++) {
                yBuf[i] = _simplexVectorW[i];
                pBuf[i] = _simplexPointsP[i];
                qBuf[i] = _simplexPointsQ[i];
            }
            return numverts;
        }

        public bool InSimplex(FVector3 w) {
            //check in case lastW is already removed
            if (w == _lastW) return true;

            //w is in the current (reduced) simplex
            int numverts = NumVertices;
            for (int i = 0; i < numverts; i++)
                if (_simplexVectorW[i] == w) return true;

            return false;
        }

        public void BackupClosest(out FVector3 v) {
            v = _cachedV;
        }

        public bool EmptySimplex {
            get {
                return NumVertices == 0;
            }
        }

        public void ComputePoints(out FVector3 p1, out FVector3 p2) {
            UpdateClosestVectorAndPoints();
            p1 = _cachedPA;
            p2 = _cachedPB;
        }

        #endregion

        public void RemoveVertex(int index) {
            _numVertices--;
            _simplexVectorW[index] = _simplexVectorW[_numVertices];
            _simplexPointsP[index] = _simplexPointsP[_numVertices];
            _simplexPointsQ[index] = _simplexPointsQ[_numVertices];
        }

        public void ReduceVertices(UsageBitfield usedVerts) {
            if ((NumVertices >= 4) && (!usedVerts.UsedVertexD)) RemoveVertex(3);
            if ((NumVertices >= 3) && (!usedVerts.UsedVertexC)) RemoveVertex(2);
            if ((NumVertices >= 2) && (!usedVerts.UsedVertexB)) RemoveVertex(1);
            if ((NumVertices >= 1) && (!usedVerts.UsedVertexA)) RemoveVertex(0);
        }

        public bool UpdateClosestVectorAndPoints() {
            if (_needsUpdate) {
                _cachedBC.Reset();
                _needsUpdate = false;

                FVector3 p, a, b, c, d;
                switch (NumVertices) {
                    case 0:
                        _cachedValidClosest = false;
                        break;
                    case 1:
                        _cachedPA = _simplexPointsP[0];
                        _cachedPB = _simplexPointsQ[0];
                        _cachedV = _cachedPA - _cachedPB;
                        _cachedBC.Reset();
                        _cachedBC.SetBarycentricCoordinates(1f, Fix64.zero, Fix64.zero, Fix64.zero);
                        _cachedValidClosest = _cachedBC.IsValid;
                        break;
                    case 2:
                        //closest point origin from line segment
                        FVector3 from = _simplexVectorW[0];
                        FVector3 to = _simplexVectorW[1];
                        //FVector3 nearest;

                        FVector3 diff = from * (-1);
                        FVector3 v = to - from;
                        Fix64 t = FVector3.Dot(v, diff);

                        if (t > 0) {
                            Fix64 dotVV = v.sqrMagnitude;
                            if (t < dotVV) {
                                t /= dotVV;
                                diff -= t * v;
                                _cachedBC.UsedVertices.UsedVertexA = true;
                                _cachedBC.UsedVertices.UsedVertexB = true;
                            } else {
                                t = 1;
                                diff -= v;
                                //reduce to 1 point
                                _cachedBC.UsedVertices.UsedVertexB = true;
                            }
                        } else {
                            t = 0;
                            //reduce to 1 point
                            _cachedBC.UsedVertices.UsedVertexA = true;
                        }

                        _cachedBC.SetBarycentricCoordinates(1 - t, t, 0, 0);
                        //nearest = from + t * v;

                        _cachedPA = _simplexPointsP[0] + t * (_simplexPointsP[1] - _simplexPointsP[0]);
                        _cachedPB = _simplexPointsQ[0] + t * (_simplexPointsQ[1] - _simplexPointsQ[0]);
                        _cachedV = _cachedPA - _cachedPB;

                        ReduceVertices(_cachedBC.UsedVertices);

                        _cachedValidClosest = _cachedBC.IsValid;
                        break;
                    case 3:
                        //closest point origin from triangle
                        p = new FVector3();
                        a = _simplexVectorW[0];
                        b = _simplexVectorW[1];
                        c = _simplexVectorW[2];

                        ClosestPtPointTriangle(p, a, b, c, ref _cachedBC);
                        _cachedPA = _simplexPointsP[0] * _cachedBC.BarycentricCoords[0] +
                                        _simplexPointsP[1] * _cachedBC.BarycentricCoords[1] +
                                        _simplexPointsP[2] * _cachedBC.BarycentricCoords[2] +
                                        _simplexPointsP[3] * _cachedBC.BarycentricCoords[3];

                        _cachedPB = _simplexPointsQ[0] * _cachedBC.BarycentricCoords[0] +
                                        _simplexPointsQ[1] * _cachedBC.BarycentricCoords[1] +
                                        _simplexPointsQ[2] * _cachedBC.BarycentricCoords[2] +
                                        _simplexPointsQ[3] * _cachedBC.BarycentricCoords[3];

                        _cachedV = _cachedPA - _cachedPB;

                        ReduceVertices(_cachedBC.UsedVertices);
                        _cachedValidClosest = _cachedBC.IsValid;
                        break;
                    case 4:
                        p = new FVector3();
                        a = _simplexVectorW[0];
                        b = _simplexVectorW[1];
                        c = _simplexVectorW[2];
                        d = _simplexVectorW[3];

                        bool hasSeperation = ClosestPtPointTetrahedron(p, a, b, c, d, ref _cachedBC);

                        if (hasSeperation) {
                            _cachedPA = _simplexPointsP[0] * _cachedBC.BarycentricCoords[0] +
                                            _simplexPointsP[1] * _cachedBC.BarycentricCoords[1] +
                                            _simplexPointsP[2] * _cachedBC.BarycentricCoords[2] +
                                            _simplexPointsP[3] * _cachedBC.BarycentricCoords[3];

                            _cachedPB = _simplexPointsQ[0] * _cachedBC.BarycentricCoords[0] +
                                            _simplexPointsQ[1] * _cachedBC.BarycentricCoords[1] +
                                            _simplexPointsQ[2] * _cachedBC.BarycentricCoords[2] +
                                            _simplexPointsQ[3] * _cachedBC.BarycentricCoords[3];

                            _cachedV = _cachedPA - _cachedPB;
                            ReduceVertices(_cachedBC.UsedVertices);
                        } else {
                            if (_cachedBC.Degenerate) {
                                _cachedValidClosest = false;
                            } else {
                                _cachedValidClosest = true;
                                //degenerate case == false, penetration = true + zero
                                _cachedV.x = _cachedV.y = _cachedV.z = Fix64.zero;
                            }
                            break; // !!!!!!!!!!!! proverit na vsakiy sluchai
                        }

                        _cachedValidClosest = _cachedBC.IsValid;

                        //closest point origin from tetrahedron
                        break;
                    default:
                        _cachedValidClosest = false;
                        break;
                }
            }

            return _cachedValidClosest;
        }

        public bool ClosestPtPointTriangle(FVector3 p, FVector3 a, FVector3 b, FVector3 c,
            ref SubSimplexClosestResult result) {
            result.UsedVertices.Reset();

            Fix64 v, w;

            // Check if P in vertex region outside A
            FVector3 ab = b - a;
            FVector3 ac = c - a;
            FVector3 ap = p - a;
            Fix64 d1 = FVector3.Dot(ab, ap);
            Fix64 d2 = FVector3.Dot(ac, ap);
            if (d1 <= Fix64.zero && d2 <= Fix64.zero) {
                result.ClosestPointOnSimplex = a;
                result.UsedVertices.UsedVertexA = true;
                result.SetBarycentricCoordinates(1, 0, 0, 0);
                return true; // a; // barycentric coordinates (1,0,0)
            }

            // Check if P in vertex region outside B
            FVector3 bp = p - b;
            Fix64 d3 = FVector3.Dot(ab, bp);
            Fix64 d4 = FVector3.Dot(ac, bp);
            if (d3 >= Fix64.zero && d4 <= d3) {
                result.ClosestPointOnSimplex = b;
                result.UsedVertices.UsedVertexB = true;
                result.SetBarycentricCoordinates(0, 1, 0, 0);

                return true; // b; // barycentric coordinates (0,1,0)
            }
            // Check if P in edge region of AB, if so return projection of P onto AB
            Fix64 vc = d1 * d4 - d3 * d2;
            if (vc <= Fix64.zero && d1 >= Fix64.zero && d3 <= Fix64.zero) {
                v = d1 / (d1 - d3);
                result.ClosestPointOnSimplex = a + v * ab;
                result.UsedVertices.UsedVertexA = true;
                result.UsedVertices.UsedVertexB = true;
                result.SetBarycentricCoordinates(1 - v, v, 0, 0);
                return true;
                //return a + v * ab; // barycentric coordinates (1-v,v,0)
            }

            // Check if P in vertex region outside C
            FVector3 cp = p - c;
            Fix64 d5 = FVector3.Dot(ab, cp);
            Fix64 d6 = FVector3.Dot(ac, cp);
            if (d6 >= Fix64.zero && d5 <= d6) {
                result.ClosestPointOnSimplex = c;
                result.UsedVertices.UsedVertexC = true;
                result.SetBarycentricCoordinates(0, 0, 1, 0);
                return true;//c; // barycentric coordinates (0,0,1)
            }

            // Check if P in edge region of AC, if so return projection of P onto AC
            Fix64 vb = d5 * d2 - d1 * d6;
            if (vb <= Fix64.zero && d2 >= Fix64.zero && d6 <= Fix64.zero) {
                w = d2 / (d2 - d6);
                result.ClosestPointOnSimplex = a + w * ac;
                result.UsedVertices.UsedVertexA = true;
                result.UsedVertices.UsedVertexC = true;
                result.SetBarycentricCoordinates(1 - w, 0, w, 0);
                return true;
                //return a + w * ac; // barycentric coordinates (1-w,0,w)
            }

            // Check if P in edge region of BC, if so return projection of P onto BC
            Fix64 va = d3 * d6 - d5 * d4;
            if (va <= Fix64.zero && (d4 - d3) >= Fix64.zero && (d5 - d6) >= Fix64.zero) {
                w = (d4 - d3) / ((d4 - d3) + (d5 - d6));

                result.ClosestPointOnSimplex = b + w * (c - b);
                result.UsedVertices.UsedVertexB = true;
                result.UsedVertices.UsedVertexC = true;
                result.SetBarycentricCoordinates(0, 1 - w, w, 0);
                return true;
                // return b + w * (c - b); // barycentric coordinates (0,1-w,w)
            }

            // P inside face region. Compute Q through its barycentric coordinates (u,v,w)
            Fix64 denom = Fix64.one / (va + vb + vc);
            v = vb * denom;
            w = vc * denom;

            result.ClosestPointOnSimplex = a + ab * v + ac * w;
            result.UsedVertices.UsedVertexA = true;
            result.UsedVertices.UsedVertexB = true;
            result.UsedVertices.UsedVertexC = true;
            result.SetBarycentricCoordinates(1 - v - w, v, w, 0);

            return true;
        }

        /// Test if point p and d lie on opposite sides of plane through abc
        public int PointOutsideOfPlane(FVector3 p, FVector3 a, FVector3 b, FVector3 c, FVector3 d) {
            FVector3 normal = FVector3.Cross(b - a, c - a);

            Fix64 signp = FVector3.Dot(p - a, normal); // [AP AB AC]
            Fix64 signd = FVector3.Dot(d - a, normal); // [AD AB AC]

            //if (CatchDegenerateTetrahedron)
            if (signd * signd < (Fix64.EN8)) return -1;

            // Points on opposite sides if expression signs are opposite
            return signp * signd < Fix64.zero ? 1 : 0;
        }

        public bool ClosestPtPointTetrahedron(FVector3 p, FVector3 a, FVector3 b, FVector3 c, FVector3 d,
            ref SubSimplexClosestResult finalResult) {
            tempResult.Reset();

            // Start out assuming point inside all halfspaces, so closest to itself
            finalResult.ClosestPointOnSimplex = p;
            finalResult.UsedVertices.Reset();
            finalResult.UsedVertices.UsedVertexA = true;
            finalResult.UsedVertices.UsedVertexB = true;
            finalResult.UsedVertices.UsedVertexC = true;
            finalResult.UsedVertices.UsedVertexD = true;

            int pointOutsideABC = PointOutsideOfPlane(p, a, b, c, d);
            int pointOutsideACD = PointOutsideOfPlane(p, a, c, d, b);
            int pointOutsideADB = PointOutsideOfPlane(p, a, d, b, c);
            int pointOutsideBDC = PointOutsideOfPlane(p, b, d, c, a);

            if (pointOutsideABC < 0 || pointOutsideACD < 0 || pointOutsideADB < 0 || pointOutsideBDC < 0) {
                finalResult.Degenerate = true;
                return false;
            }

            if (pointOutsideABC == 0 && pointOutsideACD == 0 && pointOutsideADB == 0 && pointOutsideBDC == 0)
                return false;

            Fix64 bestSqDist = Fix64.maxValue;
            // If point outside face abc then compute closest point on abc
            if (pointOutsideABC != 0) {
                ClosestPtPointTriangle(p, a, b, c, ref tempResult);
                FVector3 q = tempResult.ClosestPointOnSimplex;

                Fix64 sqDist = ((FVector3)(q - p)).sqrMagnitude;
                // Update best closest point if (squared) distance is less than current best
                if (sqDist < bestSqDist) {
                    bestSqDist = sqDist;
                    finalResult.ClosestPointOnSimplex = q;
                    //convert result bitmask!
                    finalResult.UsedVertices.Reset();
                    finalResult.UsedVertices.UsedVertexA = tempResult.UsedVertices.UsedVertexA;
                    finalResult.UsedVertices.UsedVertexB = tempResult.UsedVertices.UsedVertexB;
                    finalResult.UsedVertices.UsedVertexC = tempResult.UsedVertices.UsedVertexC;
                    finalResult.SetBarycentricCoordinates(
                            tempResult.BarycentricCoords[VertexA],
                            tempResult.BarycentricCoords[VertexB],
                            tempResult.BarycentricCoords[VertexC],
                            0);
                }
            }

            // Repeat test for face acd
            if (pointOutsideACD != 0) {
                ClosestPtPointTriangle(p, a, c, d, ref tempResult);
                FVector3 q = tempResult.ClosestPointOnSimplex;
                //convert result bitmask!

                Fix64 sqDist = ((FVector3)(q - p)).sqrMagnitude;
                if (sqDist < bestSqDist) {
                    bestSqDist = sqDist;
                    finalResult.ClosestPointOnSimplex = q;
                    finalResult.UsedVertices.Reset();
                    finalResult.UsedVertices.UsedVertexA = tempResult.UsedVertices.UsedVertexA;
                    finalResult.UsedVertices.UsedVertexC = tempResult.UsedVertices.UsedVertexB;
                    finalResult.UsedVertices.UsedVertexD = tempResult.UsedVertices.UsedVertexC;
                    finalResult.SetBarycentricCoordinates(
                            tempResult.BarycentricCoords[VertexA],
                            0,
                            tempResult.BarycentricCoords[VertexB],
                            tempResult.BarycentricCoords[VertexC]);
                }
            }
            // Repeat test for face adb

            if (pointOutsideADB != 0) {
                ClosestPtPointTriangle(p, a, d, b, ref tempResult);
                FVector3 q = tempResult.ClosestPointOnSimplex;
                //convert result bitmask!

                Fix64 sqDist = ((FVector3)(q - p)).sqrMagnitude;
                if (sqDist < bestSqDist) {
                    bestSqDist = sqDist;
                    finalResult.ClosestPointOnSimplex = q;
                    finalResult.UsedVertices.Reset();
                    finalResult.UsedVertices.UsedVertexA = tempResult.UsedVertices.UsedVertexA;
                    finalResult.UsedVertices.UsedVertexD = tempResult.UsedVertices.UsedVertexB;
                    finalResult.UsedVertices.UsedVertexB = tempResult.UsedVertices.UsedVertexC;
                    finalResult.SetBarycentricCoordinates(
                            tempResult.BarycentricCoords[VertexA],
                            tempResult.BarycentricCoords[VertexC],
                            0,
                            tempResult.BarycentricCoords[VertexB]);

                }
            }
            // Repeat test for face bdc

            if (pointOutsideBDC != 0) {
                ClosestPtPointTriangle(p, b, d, c, ref tempResult);
                FVector3 q = tempResult.ClosestPointOnSimplex;
                //convert result bitmask!
                Fix64 sqDist = ((FVector3)(q - p)).sqrMagnitude;
                if (sqDist < bestSqDist) {
                    bestSqDist = sqDist;
                    finalResult.ClosestPointOnSimplex = q;
                    finalResult.UsedVertices.Reset();
                    finalResult.UsedVertices.UsedVertexB = tempResult.UsedVertices.UsedVertexA;
                    finalResult.UsedVertices.UsedVertexD = tempResult.UsedVertices.UsedVertexB;
                    finalResult.UsedVertices.UsedVertexC = tempResult.UsedVertices.UsedVertexC;

                    finalResult.SetBarycentricCoordinates(
                            0,
                            tempResult.BarycentricCoords[VertexA],
                            tempResult.BarycentricCoords[VertexC],
                            tempResult.BarycentricCoords[VertexB]);
                }
            }

            //help! we ended up full !

            if (finalResult.UsedVertices.UsedVertexA &&
                finalResult.UsedVertices.UsedVertexB &&
                finalResult.UsedVertices.UsedVertexC &&
                finalResult.UsedVertices.UsedVertexD) {
                return true;
            }

            return true;
        }
    }

    #endregion

    public class UsageBitfield {
        private bool _usedVertexA, _usedVertexB, _usedVertexC, _usedVertexD;

        public bool UsedVertexA { get { return _usedVertexA; } set { _usedVertexA = value; } }
        public bool UsedVertexB { get { return _usedVertexB; } set { _usedVertexB = value; } }
        public bool UsedVertexC { get { return _usedVertexC; } set { _usedVertexC = value; } }
        public bool UsedVertexD { get { return _usedVertexD; } set { _usedVertexD = value; } }

        public void Reset() {
            _usedVertexA = _usedVertexB = _usedVertexC = _usedVertexD = false;
        }
    }

    public class SubSimplexClosestResult {
        private FVector3 _closestPointOnSimplex;

        //MASK for m_usedVertices
        //stores the simplex vertex-usage, using the MASK, 
        // if m_usedVertices & MASK then the related vertex is used
        private UsageBitfield _usedVertices = new UsageBitfield();
        private Fix64[] _barycentricCoords = new Fix64[4];
        private bool _degenerate;

        public FVector3 ClosestPointOnSimplex { get { return _closestPointOnSimplex; } set { _closestPointOnSimplex = value; } }
        public UsageBitfield UsedVertices { get { return _usedVertices; } set { _usedVertices = value; } }
        public Fix64[] BarycentricCoords { get { return _barycentricCoords; } set { _barycentricCoords = value; } }
        public bool Degenerate { get { return _degenerate; } set { _degenerate = value; } }

        public void Reset() {
            _degenerate = false;
            SetBarycentricCoordinates();
            _usedVertices.Reset();
        }

        public bool IsValid {
            get {
                return (_barycentricCoords[0] >= Fix64.zero) &&
                        (_barycentricCoords[1] >= Fix64.zero) &&
                        (_barycentricCoords[2] >= Fix64.zero) &&
                        (_barycentricCoords[3] >= Fix64.zero);
            }
        }

        public void SetBarycentricCoordinates() {
            SetBarycentricCoordinates(Fix64.zero, Fix64.zero, Fix64.zero, Fix64.zero);
        }

        public void SetBarycentricCoordinates(Fix64 a, Fix64 b, Fix64 c, Fix64 d) {
            _barycentricCoords[0] = a;
            _barycentricCoords[1] = b;
            _barycentricCoords[2] = c;
            _barycentricCoords[3] = d;
        }
    }

}