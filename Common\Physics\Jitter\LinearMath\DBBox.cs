﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

using FixedMath;

namespace Common.Physics
{
    /// <summary>
    /// detrministic Bounding Box defined through min and max vectors.
    /// </summary>
    public struct DBBox
    {
        /// <summary>
        /// Containment type used within the <see cref="DBBox"/> structure.
        /// </summary>
        public enum ContainmentType
        {
            /// <summary>
            /// The objects don't intersect.
            /// </summary>
            Disjoint,
            /// <summary>
            /// One object is within the other.
            /// </summary>
            Contains,
            /// <summary>
            /// The two objects intersect.
            /// </summary>
            Intersects
        }

        /// <summary>
        /// The maximum point of the box.
        /// </summary>
        public FVector3 min;

        /// <summary>
        /// The minimum point of the box.
        /// </summary>
        public FVector3 max;

        /// <summary>
        /// Returns the largest box possible.
        /// </summary>
        public static readonly DBBox LargeBox;

        /// <summary>
        /// Returns the smalltest box possible.
        /// </summary>
        public static readonly DBBox SmallBox;

        static DBBox()
        {
            LargeBox.min = new FVector3(Fix64.minValue);
            LargeBox.max = new FVector3(Fix64.maxValue);
            SmallBox.min = new FVector3(Fix64.maxValue);
            SmallBox.max = new FVector3(Fix64.minValue);
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="min">The minimum point of the box.</param>
        /// <param name="max">The maximum point of the box.</param>
        public DBBox(FVector3 min, FVector3 max)
        {
            this.min = min;
            this.max = max;
        }

        /// <summary>
        /// Transforms the bounding box into the space given by orientation and position.
        /// </summary>
        /// <param name="position"></param>
        /// <param name="orientation"></param>
        /// <param name="result"></param>
        internal void InverseTransform(ref FVector3 position, ref FMatrix3x3 orientation)
        {
            FVector3.Subtract(ref max, ref position, out max);
            FVector3.Subtract(ref min, ref position, out min);

            FVector3 center;
            FVector3.Add(ref max, ref min, out center);
            center.x *= Fix64.half; center.y *= Fix64.half; center.z *= Fix64.half;

            FVector3 halfExtents;
            FVector3.Subtract(ref max, ref min, out halfExtents);
            halfExtents.x *= Fix64.half; halfExtents.y *= Fix64.half; halfExtents.z *= Fix64.half;

            FVector3.TransposedTransform(ref center, ref orientation, out center);

            FMatrix3x3 abs; DMath.Absolute(ref orientation, out abs);
            FVector3.TransposedTransform(ref halfExtents, ref abs, out halfExtents);

            FVector3.Add(ref center, ref halfExtents, out max);
            FVector3.Subtract(ref center, ref halfExtents, out min);
        }

        public void Transform(ref FMatrix3x3 orientation)
        {
            FVector3 halfExtents = Fix64.half * (max - min);
            FVector3 center = Fix64.half * (max + min);

            FVector3.Transform(ref center, ref orientation, out center);

            FMatrix3x3 abs; DMath.Absolute(ref orientation, out abs);
            FVector3.Transform(ref halfExtents, ref abs, out halfExtents);

            max = center + halfExtents;
            min = center - halfExtents;
        }

        /// <summary>
        /// Checks whether a point is inside, outside or intersecting
        /// a point.
        /// </summary>
        /// <returns>The ContainmentType of the point.</returns>
        #region public Ray/Segment Intersection

        private bool Intersect1D(Fix64 start, Fix64 dir, Fix64 min, Fix64 max,
            ref Fix64 enter,ref Fix64 exit)
        {
            if (dir * dir < DMath.Epsilon * DMath.Epsilon) return (start >= min && start <= max);

            Fix64 t0 = (min - start) / dir;
            Fix64 t1 = (max - start) / dir;

            if (t0 > t1) { Fix64 tmp = t0; t0 = t1; t1 = tmp; }

            if (t0 > exit || t1 < enter) return false;

            if (t0 > enter) enter = t0;
            if (t1 < exit) exit = t1;
            return true;
        }


        public bool SegmentIntersect(ref FVector3 origin, ref FVector3 direction)
        {
            Fix64 enter = Fix64.zero, exit = Fix64.one;

            if (!Intersect1D(origin.x, direction.x, min.x, max.x,ref enter,ref exit))
                return false;

            if (!Intersect1D(origin.y, direction.y, min.y, max.y, ref enter, ref exit))
                return false;

            if (!Intersect1D(origin.z, direction.z, min.z, max.z,ref enter,ref exit))
                return false;

            return true;
        }

        public bool RayIntersect(ref FVector3 origin, ref FVector3 direction)
        {
            Fix64 enter = Fix64.zero, exit = Fix64.maxValue;

            if (!Intersect1D(origin.x, direction.x, min.x, max.x, ref enter, ref exit))
                return false;

            if (!Intersect1D(origin.y, direction.y, min.y, max.y, ref enter, ref exit))
                return false;

            if (!Intersect1D(origin.z, direction.z, min.z, max.z, ref enter, ref exit))
                return false;

            return true;
        }

        public bool SegmentIntersect(FVector3 origin, FVector3 direction)
        {
            return SegmentIntersect(ref origin, ref direction);
        }

        public bool RayIntersect(FVector3 origin, FVector3 direction)
        {
            return RayIntersect(ref origin, ref direction);
        }

        /// <summary>
        /// Checks wether a point is within a box or not.
        /// </summary>
        /// <param name="point"></param>
        /// <returns></returns>
        public ContainmentType Contains(FVector3 point)
        {
            return this.Contains(ref point);
        }

        /// <summary>
        /// Checks whether a point is inside, outside or intersecting
        /// a point.
        /// </summary>
        /// <param name="point">A point in space.</param>
        /// <returns>The ContainmentType of the point.</returns>
        public ContainmentType Contains(ref FVector3 point)
        {
            return ((((this.min.x <= point.x) && (point.x <= this.max.x)) &&
                ((this.min.y <= point.y) && (point.y <= this.max.y))) &&
                ((this.min.z <= point.z) && (point.z <= this.max.z))) ? ContainmentType.Contains : ContainmentType.Disjoint;
        }

        #endregion

        /// <summary>
        /// Retrieves the 8 corners of the box.
        /// </summary>
        /// <returns>An array of 8 JVector entries.</returns>
        #region public void GetCorners(JVector[] corners)

        public void GetCorners(FVector3[] corners)
        {
            corners[0].Set(this.min.x, this.max.y, this.max.z);
            corners[1].Set(this.max.x, this.max.y, this.max.z);
            corners[2].Set(this.max.x, this.min.y, this.max.z);
            corners[3].Set(this.min.x, this.min.y, this.max.z);
            corners[4].Set(this.min.x, this.max.y, this.min.z);
            corners[5].Set(this.max.x, this.max.y, this.min.z);
            corners[6].Set(this.max.x, this.min.y, this.min.z);
            corners[7].Set(this.min.x, this.min.y, this.min.z);
        }

        #endregion


        public void AddPoint(FVector3 point)
        {
            AddPoint(ref point);
        }

        public void AddPoint(ref FVector3 point)
        {
            FVector3.Max(ref this.max, ref point, out this.max);
            FVector3.Min(ref this.min, ref point, out this.min);
        }

        /// <summary>
        /// Expands a bounding box with the volume 0 by all points
        /// given.
        /// </summary>
        /// <param name="points">A array of JVector.</param>
        /// <returns>The resulting bounding box containing all points.</returns>
        #region public static JBBox CreateFromPoints(JVector[] points)

        public static DBBox CreateFromPoints(FVector3[] points)
        {
            FVector3 vector3 = new FVector3(Fix64.maxValue);
            FVector3 vector2 = new FVector3(Fix64.minValue);

            for (int i = 0; i < points.Length; i++)
            {
                FVector3.Min(ref vector3, ref points[i], out vector3);
                FVector3.Max(ref vector2, ref points[i], out vector2);
            }
            return new DBBox(vector3, vector2);
        }

        #endregion

        /// <summary>
        /// Checks whether another bounding box is inside, outside or intersecting
        /// this box. 
        /// </summary>
        /// <param name="box">The other bounding box to check.</param>
        /// <returns>The ContainmentType of the box.</returns>
        #region public ContainmentType Contains(JBBox box)

        public ContainmentType Contains(DBBox box)
        {
            return this.Contains(ref box);
        }

        /// <summary>
        /// Checks whether another bounding box is inside, outside or intersecting
        /// this box. 
        /// </summary>
        /// <param name="box">The other bounding box to check.</param>
        /// <returns>The ContainmentType of the box.</returns>
        public ContainmentType Contains(ref DBBox box)
        {
            ContainmentType result = ContainmentType.Disjoint;
            if ((((this.max.x >= box.min.x) && (this.min.x <= box.max.x)) && ((this.max.y >= box.min.y) && (this.min.y <= box.max.y))) && ((this.max.z >= box.min.z) && (this.min.z <= box.max.z)))
            {
                result = ((((this.min.x <= box.min.x) && (box.max.x <= this.max.x)) && ((this.min.y <= box.min.y) && (box.max.y <= this.max.y))) && ((this.min.z <= box.min.z) && (box.max.z <= this.max.z))) ? ContainmentType.Contains : ContainmentType.Intersects;
            }

            return result;
        }

        #endregion

		public static DBBox CreateFromCenter(FVector3 center, FVector3 size) {
            FVector3 half = size * Fix64.half;
			return new DBBox (center - half, center + half);
		}

        /// <summary>
        /// Creates a new box containing the two given ones.
        /// </summary>
        /// <param name="original">First box.</param>
        /// <param name="additional">Second box.</param>
        /// <returns>A JBBox containing the two given boxes.</returns>
        #region public static TSBBox CreateMerged(TSBBox original, TSBBox additional)

        public static DBBox CreateMerged(DBBox original, DBBox additional)
        {
            DBBox result;
            DBBox.CreateMerged(ref original, ref additional, out result);
            return result;
        }

        /// <summary>
        /// Creates a new box containing the two given ones.
        /// </summary>
        /// <param name="original">First box.</param>
        /// <param name="additional">Second box.</param>
        /// <param name="result">A JBBox containing the two given boxes.</param>
        public static void CreateMerged(ref DBBox original, ref DBBox additional, out DBBox result)
        {
            FVector3 vector;
            FVector3 vector2;
            FVector3.Min(ref original.min, ref additional.min, out vector2);
            FVector3.Max(ref original.max, ref additional.max, out vector);
            result.min = vector2;
            result.max = vector;
        }

        #endregion

        public FVector3 center { get { return (min + max) * (Fix64.half); } }

        public FVector3 size {
            get {
                return (max - min);
            }
        }

        public FVector3 extents {
            get {
                return size * Fix64.half;
            }
        }

        internal Fix64 Perimeter    //周长
        {
            get
            {
                return (2 * Fix64.one) * ((max.x - min.x) * (max.y - min.y) +
                    (max.x - min.x) * (max.z - min.z) +
                    (max.z - min.z) * (max.y - min.y));
            }
        }

        public override string ToString() {
            return min + "|" + max;
        }

    }
}
