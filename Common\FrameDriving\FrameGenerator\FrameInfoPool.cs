﻿
// using GCloud.LockStep;

namespace Common.FrameDriving
{
    public class FrameInfoPoolable : Common.Common.FS_PoolableObject
    {
        FrameInfo _frameInfo;
        public int TimeSpan;

        public FrameInfoPoolable()
        {
            _frameInfo = new FrameInfo();
        }

        public FrameInfo frameInfo
        {
            get
            {
                return _frameInfo;
            }
        }

        public override void OnReuse() { }

        protected override void ResetAllFields() { }


        protected override void OnRelease()
        {
            _frameInfo.FrameId = 0;
            _frameInfo.RecvTickMS = 0;
            _frameInfo.ValidDataCount = 0;
        }
    }
}

