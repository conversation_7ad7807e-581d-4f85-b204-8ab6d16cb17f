using System;
using GP.Common.Statescript;
using GP.Common.Statescript.Serialization;

namespace GP.Common.Statescript.Examples
{
    /// <summary>
    /// Test class to verify the new type-safe variable system
    /// </summary>
    public static class StatescriptVariableTest
    {
        /// <summary>
        /// Test all variable types for serialization and type safety
        /// </summary>
        public static void RunVariableTests()
        {
            Console.WriteLine("=== Statescript Variable System Tests ===");
            Console.WriteLine();

            TestBasicVariableOperations();
            TestVariableSerialization();
            TestVariableTypeConversions();
            TestVariableInGraph();

            Console.WriteLine("All variable tests completed!");
        }

        private static void TestBasicVariableOperations()
        {
            Console.WriteLine("Testing basic variable operations...");

            // Test Boolean variable
            var boolVar = new StatescriptVariable("TestBool", StatescriptVariableType.Boolean, true);
            Console.WriteLine($"Boolean: {boolVar}");
            Console.WriteLine($"  Get<bool>(): {boolVar.GetValue<bool>()}");
            Console.WriteLine($"  GetValueAsObject(): {boolVar.GetValueAsObject()}");

            // Test Integer variable
            var intVar = new StatescriptVariable("TestInt", StatescriptVariableType.Integer, 42);
            Console.WriteLine($"Integer: {intVar}");
            Console.WriteLine($"  Get<int>(): {intVar.GetValue<int>()}");
            Console.WriteLine($"  GetValueAsObject(): {intVar.GetValueAsObject()}");

            // Test Float variable
            var floatVar = new StatescriptVariable("TestFloat", StatescriptVariableType.Float, 3.14f);
            Console.WriteLine($"Float: {floatVar}");
            Console.WriteLine($"  Get<float>(): {floatVar.GetValue<float>()}");
            Console.WriteLine($"  GetValueAsObject(): {floatVar.GetValueAsObject()}");

            // Test String variable
            var stringVar = new StatescriptVariable("TestString", StatescriptVariableType.String, "Hello World");
            Console.WriteLine($"String: {stringVar}");
            Console.WriteLine($"  Get<string>(): {stringVar.GetValue<string>()}");
            Console.WriteLine($"  GetValueAsObject(): {stringVar.GetValueAsObject()}");

            // Test Vector2 variable
            var vec2Var = new StatescriptVariable("TestVec2", StatescriptVariableType.Vector2, new FVector2(Fix64.one, Fix64.one * 2));
            Console.WriteLine($"Vector2: {vec2Var}");
            Console.WriteLine($"  Get<FVector2>(): {vec2Var.GetValue<FVector2>()}");
            Console.WriteLine($"  GetValueAsObject(): {vec2Var.GetValueAsObject()}");

            // Test Vector3 variable
            var vec3Var = new StatescriptVariable("TestVec3", StatescriptVariableType.Vector3, new FVector3(Fix64.one, Fix64.one * 2, Fix64.one * 3));
            Console.WriteLine($"Vector3: {vec3Var}");
            Console.WriteLine($"  Get<FVector3>(): {vec3Var.GetValue<FVector3>()}");
            Console.WriteLine($"  GetValueAsObject(): {vec3Var.GetValueAsObject()}");

            // Test Entity variable (stored as ID)
            var entityVar = new StatescriptVariable("TestEntity", StatescriptVariableType.Entity);
            entityVar.SetEntityValue(12345);
            Console.WriteLine($"Entity: {entityVar}");
            Console.WriteLine($"  Get<int>(): {entityVar.GetValue<int>()}");
            Console.WriteLine($"  GetValueAsObject(): {entityVar.GetValueAsObject()}");

            Console.WriteLine();
        }

        private static void TestVariableSerialization()
        {
            Console.WriteLine("Testing variable serialization...");

            var graph = new StatescriptGraph("Variable Test Graph");

            // Add variables of all types
            graph.Variables.Add(new StatescriptVariable("BoolVar", StatescriptVariableType.Boolean, true));
            graph.Variables.Add(new StatescriptVariable("IntVar", StatescriptVariableType.Integer, 42));
            graph.Variables.Add(new StatescriptVariable("FloatVar", StatescriptVariableType.Float, 3.14f));
            graph.Variables.Add(new StatescriptVariable("StringVar", StatescriptVariableType.String, "Test String"));
            graph.Variables.Add(new StatescriptVariable("Vec2Var", StatescriptVariableType.Vector2, new FVector2(Fix64.one, Fix64.one * 2)));
            graph.Variables.Add(new StatescriptVariable("Vec3Var", StatescriptVariableType.Vector3, new FVector3(Fix64.one, Fix64.one * 2, Fix64.one * 3)));

            var entityVar = new StatescriptVariable("EntityVar", StatescriptVariableType.Entity);
            entityVar.SetEntityValue(999);
            graph.Variables.Add(entityVar);

            // Test serialization formats
            var formats = new[] 
            { 
                StatescriptSerializationFormat.Json,
                StatescriptSerializationFormat.Binary,
                StatescriptSerializationFormat.CompressedBinary
            };

            foreach (var format in formats)
            {
                Console.WriteLine($"  Testing {format} format...");
                
                try
                {
                    // Serialize
                    var data = StatescriptSerializer.Serialize(graph, format);
                    Console.WriteLine($"    Serialized: {data.Length} bytes");

                    // Deserialize
                    var deserializedGraph = StatescriptSerializer.Deserialize(data, format);
                    Console.WriteLine($"    Deserialized: {deserializedGraph.Variables.Count} variables");

                    // Verify variable values
                    foreach (var variable in deserializedGraph.Variables)
                    {
                        var originalVar = graph.GetVariable(variable.Name);
                        var originalValue = originalVar.GetValueAsObject();
                        var deserializedValue = variable.GetValueAsObject();
                        
                        bool valuesMatch = originalValue?.ToString() == deserializedValue?.ToString();
                        Console.WriteLine($"      {variable.Name}: {(valuesMatch ? "✓" : "✗")} ({originalValue} -> {deserializedValue})");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"    Error: {ex.Message}");
                }
            }

            Console.WriteLine();
        }

        private static void TestVariableTypeConversions()
        {
            Console.WriteLine("Testing variable type conversions...");

            var intVar = new StatescriptVariable("TestInt", StatescriptVariableType.Integer, 42);

            // Test valid conversions
            Console.WriteLine($"  Int to float: {intVar.GetValue<float>()}");
            Console.WriteLine($"  Int to string: {intVar.GetValue<string>()}");
            Console.WriteLine($"  Int to bool (fallback): {intVar.GetValue<bool>(true)}");

            // Test setting different types
            var stringVar = new StatescriptVariable("TestString", StatescriptVariableType.String);
            Console.WriteLine($"  Set string with int: {stringVar.SetValue(123)} -> {stringVar.GetValue<string>()}");
            Console.WriteLine($"  Set string with bool: {stringVar.SetValue(true)} -> {stringVar.GetValue<string>()}");

            var boolVar = new StatescriptVariable("TestBool", StatescriptVariableType.Boolean);
            Console.WriteLine($"  Set bool with int: {boolVar.SetValue(1)} -> {boolVar.GetValue<bool>()}");
            Console.WriteLine($"  Set bool with string: {boolVar.SetValue("true")} -> {boolVar.GetValue<bool>()}");

            Console.WriteLine();
        }

        private static void TestVariableInGraph()
        {
            Console.WriteLine("Testing variables in graph execution...");

            var graph = new StatescriptGraph("Variable Graph Test");

            // Add a counter variable
            var counterVar = new StatescriptVariable("Counter", StatescriptVariableType.Integer, 0);
            graph.Variables.Add(counterVar);

            // Create nodes that use variables
            var entryNode = new EntryNode { Id = 1, Name = "Entry" };
            
            var getVarNode = new GetVariableNode { Id = 2, Name = "Get Counter", VariableName = "Counter" };
            
            var setVarNode = new SetVariableNode { Id = 3, Name = "Increment Counter", VariableName = "Counter", NewValue = 1 };
            
            var logNode = new LogNode { Id = 4, Name = "Log Counter" };
            logNode.SetProperty("Message", "Counter value updated");

            // Add nodes
            graph.AddNode(entryNode);
            graph.AddNode(getVarNode);
            graph.AddNode(setVarNode);
            graph.AddNode(logNode);

            // Connect nodes
            graph.AddConnection(new StatescriptConnection(1, 2) { Id = 1 });
            graph.AddConnection(new StatescriptConnection(2, 3) { Id = 2 });
            graph.AddConnection(new StatescriptConnection(3, 4) { Id = 3 });

            // Execute graph
            var context = new StatescriptContext();
            graph.Initialize(context);

            Console.WriteLine($"  Initial counter value: {graph.GetVariableValue<int>("Counter")}");
            
            graph.Start();
            
            // Simulate a few frames
            for (int i = 0; i < 10 && graph.IsRunning; i++)
            {
                graph.Update(0.016f); // 60 FPS
            }

            Console.WriteLine($"  Final counter value: {graph.GetVariableValue<int>("Counter")}");
            Console.WriteLine($"  Graph completed: {!graph.IsRunning}");

            Console.WriteLine();
        }
    }
}
