
using System.Diagnostics;

namespace Common.FrameDriving
{
    /// <summary>
    /// 默认的FrameDispatcher实现
    /// </summary>
    class DefaultFrameDispatcher : IFrameDispatcher
    {
        /// <summary>
        /// 设置一个数值，规定每次最多可以分发多少帧
        /// </summary>
        private uint _maxFramePerDispatch = uint.MaxValue;
        public uint maxFramePerDispatch
        {
            get
            {
                return _maxFramePerDispatch;
            }

            set
            {
                _maxFramePerDispatch = value;
            }
        }

        /// <summary>
        /// 分发帧。
        /// </summary>
        /// <param name="frameDriver">frame的第一个接受者</param>
        public virtual void DispatchFrames(FrameDriver frameDriver)
        {
            Debug.Assert(null != frameDriver && null != frameDriver.frameQueue);

            int frameCounter = 0;

            while (frameDriver.frameQueue.Count > 0 && frameCounter < maxFramePerDispatch)
            {
                Frame frame = frameDriver.frameQueue.Dequeue();
                Debug.Assert(null != frame);

                frameDriver.DoFrame(frame);
                frameCounter++;

                if (null != frameDriver.frameGenerator)
                {
                    frameDriver.frameGenerator.OnFrameDispatched(frame);  //可以通过对象池来进行优化
                }

                if (null != frameDriver.framePool)
                {
                    frameDriver.framePool.Free(frame);
                }
            }
        } 
    }
}