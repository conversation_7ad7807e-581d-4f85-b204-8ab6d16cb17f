﻿using MemoryPack.Internal;

namespace MemoryPack.Formatters;

[Preserve]
public sealed class VersionFormatter : MemoryPackFormatter<Version>
{
    // Serialize as [Major, Minor, Build, Revision]

    [Preserve]
    public override void Serialize<TBufferWriter>(ref MemoryPackWriter<TBufferWriter> writer, scoped ref Version? value)
    {
        if (value == null)
        {
            writer.WriteNullObjectHeader();
            return;
        }

        writer.WriteUnmanagedWithObjectHeader(4, value.Major, value.Minor, value.Build, value.Revision);
    }

    [Preserve]
    public override void Deserialize(ref MemoryPackReader reader, scoped ref Version? value)
    {
        if (!reader.TryReadObjectHeader(out var count))
        {
            value = null;
            return;
        }

        if (count != 4) MemoryPackSerializationException.ThrowInvalidPropertyCount(4, count);

        reader.ReadUnmanaged(out int major, out int minor, out int build, out int revision);

        // when use new Version(major, minor), build and revision will be -1, it can not use constructor.
        if (revision == -1)
        {
            if (build == -1)
            {
                value = new Version(major, minor);
            }
            else
            {
                value = new Version(major, minor, build);
            }
        }
        else
        {
            value = new Version(major, minor, build, revision);
        }
    }
}
