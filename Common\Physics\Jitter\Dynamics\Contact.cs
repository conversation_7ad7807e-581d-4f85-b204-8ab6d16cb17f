﻿/* Copyright (C) <2009-2011> <<PERSON><PERSON>, Jitter Physics>
* 
*  This software is provided 'as-is', without any express or implied
*  warranty.  In no event will the authors be held liable for any damages
*  arising from the use of this software.
*
*  Permission is granted to anyone to use this software for any purpose,
*  including commercial applications, and to alter it and redistribute it
*  freely, subject to the following restrictions:
*
*  1. The origin of this software must not be misrepresented; you must not
*      claim that you wrote the original software. If you use this software
*      in a product, an acknowledgment in the product documentation would be
*      appreciated but is not required.
*  2. Altered source versions must be plainly marked as such, and must not be
*      misrepresented as being the original software.
*  3. This notice may not be removed or altered from any source distribution. 
*/

using FixedMath;

namespace Common.Physics {

    #region public class ContactSettings
    public class ContactSettings
    {
        public enum MaterialCoefficientMixingType { TakeMaximum, TakeMinimum, UseAverage }

        internal Fix64 maximumBias = 10;
        internal Fix64 bias = 25 * Fix64.EN2;
        internal Fix64 minVelocity = Fix64.EN3;
        internal Fix64 allowedPenetration = Fix64.EN2;
        internal Fix64 breakThreshold = Fix64.EN2;

        internal MaterialCoefficientMixingType materialMode = MaterialCoefficientMixingType.UseAverage;

        public Fix64 MaximumBias { get { return maximumBias; } set { maximumBias = value; } }

        public Fix64 BiasFactor { get { return bias; } set { bias = value; } }

        public Fix64 MinimumVelocity { get { return minVelocity; } set { minVelocity = value; } }

        public Fix64 AllowedPenetration { get { return allowedPenetration; } set { allowedPenetration = value; } }

        public Fix64 BreakThreshold { get { return breakThreshold; } set { breakThreshold = value; } }

        public MaterialCoefficientMixingType MaterialCoefficientMixing { get { return materialMode; } set { materialMode = value; } }
    }
    #endregion


    /// <summary>
    /// </summary>
    public class Contact : IConstraint
    {
        public ContactSettings settings;

		public RigidBody body1, body2;

		public FVector3 normal, tangent;

		public FVector3 realRelPos1, realRelPos2;
		public FVector3 relativePos1, relativePos2;
		public FVector3 p1, p2;

		public Fix64 accumulatedNormalImpulse = Fix64.zero;
		public Fix64 accumulatedTangentImpulse = Fix64.zero;

		public Fix64 penetration = Fix64.zero;
		public Fix64 initialPen = Fix64.zero;

		public Fix64 staticFriction, dynamicFriction, restitution;
		public Fix64 friction = Fix64.zero;

		public Fix64 massNormal = Fix64.zero, massTangent = Fix64.zero;
		public Fix64 restitutionBias = Fix64.zero;

		public bool newContact = false;

		public bool treatBody1AsStatic = false;
		public bool treatBody2AsStatic = false;


		public bool body1IsMassPoint;
		public bool body2IsMassPoint;

		public Fix64 lostSpeculativeBounce = Fix64.zero;
		public Fix64 speculativeVelocity = Fix64.zero;

        /// <summary>
        /// A contact resource pool.
        /// </summary>
        public static readonly ResourcePool<Contact> Pool =
            new ResourcePool<Contact>();

		public Fix64 lastTimeStep = Fix64.positiveInfinity;

        #region Properties
        public Fix64 Restitution
        {
            get { return restitution; }
            set { restitution = value; }
        }

        public Fix64 StaticFriction
        {
            get { return staticFriction; }
            set { staticFriction = value; }
        }

        public Fix64 DynamicFriction
        {
            get { return dynamicFriction; }
            set { dynamicFriction = value; }
        }

        /// <summary>
        /// The first body involved in the contact.
        /// </summary>
        public RigidBody Body1 { get { return body1; } }

        /// <summary>
        /// The second body involved in the contact.
        /// </summary>
        public RigidBody Body2 { get { return body2; } }

        /// <summary>
        /// The penetration of the contact.
        /// </summary>
        public Fix64 Penetration { get { return penetration; } }

        /// <summary>
        /// The collision position in world space of body1.
        /// </summary>
        public FVector3 Position1 { get { return p1; } }

        /// <summary>
        /// The collision position in world space of body2.
        /// </summary>
        public FVector3 Position2 { get { return p2; } }

        /// <summary>
        /// The contact tangent.
        /// </summary>
        public FVector3 Tangent { get { return tangent; } }

        /// <summary>
        /// The contact normal.
        /// </summary>
        public FVector3 Normal { get { return normal; } }
        #endregion

        /// <summary>
        /// Calculates relative velocity of body contact points on the bodies.
        /// </summary>
        /// <param name="relVel">The relative velocity of body contact points on the bodies.</param>
        public FVector3 CalculateRelativeVelocity()
        {
            Fix64 x, y, z;

            x = (body2.angularVelocity.y * relativePos2.z) - (body2.angularVelocity.z * relativePos2.y) + body2.linearVelocity.x;
            y = (body2.angularVelocity.z * relativePos2.x) - (body2.angularVelocity.x * relativePos2.z) + body2.linearVelocity.y;
            z = (body2.angularVelocity.x * relativePos2.y) - (body2.angularVelocity.y * relativePos2.x) + body2.linearVelocity.z;

            FVector3 relVel;
            relVel.x = x - (body1.angularVelocity.y * relativePos1.z) + (body1.angularVelocity.z * relativePos1.y) - body1.linearVelocity.x;
            relVel.y = y - (body1.angularVelocity.z * relativePos1.x) + (body1.angularVelocity.x * relativePos1.z) - body1.linearVelocity.y;
            relVel.z = z - (body1.angularVelocity.x * relativePos1.y) + (body1.angularVelocity.y * relativePos1.x) - body1.linearVelocity.z;

            return relVel;
        }

        /// <summary>
        /// Solves the contact iteratively.
        /// </summary>
        /// christ 解接触约束，如果物体渗透了，给一个反向的速度让物体退回到正好发生碰撞，或者没有穿透的情形
        public void Iterate()
        {
            //body1.linearVelocity = FVector3.zero;
            //body2.linearVelocity = FVector3.zero;
            //return;

            if (treatBody1AsStatic && treatBody2AsStatic) return;

            Fix64 dvx, dvy, dvz;

            dvx = body2.linearVelocity.x - body1.linearVelocity.x;  //body2相对body1的线速度
            dvy = body2.linearVelocity.y - body1.linearVelocity.y;
            dvz = body2.linearVelocity.z - body1.linearVelocity.z;

            if (!body1IsMassPoint)
            {
                dvx = dvx - (body1.angularVelocity.y * relativePos1.z) + (body1.angularVelocity.z * relativePos1.y);
                dvy = dvy - (body1.angularVelocity.z * relativePos1.x) + (body1.angularVelocity.x * relativePos1.z);
                dvz = dvz - (body1.angularVelocity.x * relativePos1.y) + (body1.angularVelocity.y * relativePos1.x);
            }

            if (!body2IsMassPoint)
            {
                dvx = dvx + (body2.angularVelocity.y * relativePos2.z) - (body2.angularVelocity.z * relativePos2.y);
                dvy = dvy + (body2.angularVelocity.z * relativePos2.x) - (body2.angularVelocity.x * relativePos2.z);
                dvz = dvz + (body2.angularVelocity.x * relativePos2.y) - (body2.angularVelocity.y * relativePos2.x);
            }

            // this gets us some performance
            if (dvx * dvx + dvy * dvy + dvz * dvz < settings.minVelocity * settings.minVelocity)
            { 
                return; 
            }

            Fix64 vn = normal.x * dvx + normal.y * dvy + normal.z * dvz;
            Fix64 normalImpulse = massNormal * (-vn + restitutionBias + speculativeVelocity);

            Fix64 oldNormalImpulse = accumulatedNormalImpulse;
            accumulatedNormalImpulse = oldNormalImpulse + normalImpulse;
            if (accumulatedNormalImpulse < Fix64.zero) accumulatedNormalImpulse = Fix64.zero;
            normalImpulse = accumulatedNormalImpulse - oldNormalImpulse;

            Fix64 vt = dvx * tangent.x + dvy * tangent.y + dvz * tangent.z;
            Fix64 maxTangentImpulse = friction * accumulatedNormalImpulse;
            Fix64 tangentImpulse = massTangent * (-vt);

            Fix64 oldTangentImpulse = accumulatedTangentImpulse;
            accumulatedTangentImpulse = oldTangentImpulse + tangentImpulse;
            if (accumulatedTangentImpulse < -maxTangentImpulse) accumulatedTangentImpulse = -maxTangentImpulse;
            else if (accumulatedTangentImpulse > maxTangentImpulse) accumulatedTangentImpulse = maxTangentImpulse;

            tangentImpulse = accumulatedTangentImpulse - oldTangentImpulse;

            // Apply contact impulse
            FVector3 impulse = normal * normalImpulse + tangent * tangentImpulse;
            ApplyImpulse(ref impulse);

        }

        public Fix64 AppliedNormalImpulse { get { return accumulatedNormalImpulse; } }
        public Fix64 AppliedTangentImpulse { get { return accumulatedTangentImpulse; } }

        /// <summary>
        /// The points in wolrd space gets recalculated by transforming the
        /// local coordinates. Also new penetration depth is estimated.
        /// </summary>
        public void UpdatePosition()
        {
            if (body1IsMassPoint)
            {
                FVector3.Add(ref realRelPos1, ref body1.position, out p1);
            }
            else
            {
                FVector3.Transform(ref realRelPos1, ref body1.orientation, out p1);
                FVector3.Add(ref p1, ref body1.position, out p1);
            }

            if (body2IsMassPoint)
            {
                FVector3.Add(ref realRelPos2, ref body2.position, out p2);
            }
            else
            {
                FVector3.Transform(ref realRelPos2, ref body2.orientation, out p2);
                FVector3.Add(ref p2, ref body2.position, out p2);
            }


            FVector3 dist; FVector3.Subtract(ref p1, ref p2, out dist);
            penetration = FVector3.Dot(ref dist, ref normal);
        }

        /// <summary>
        /// An impulse is applied an both contact points.
        /// </summary>
        /// <param name="impulse">The impulse to apply.</param>
        public void ApplyImpulse(ref FVector3 impulse)
        {
            #region INLINE - HighFrequency
            //JVector temp;

            if (!treatBody1AsStatic)
            {
                body1.linearVelocity.x -= (impulse.x * body1.inverseMass);
                body1.linearVelocity.y -= (impulse.y * body1.inverseMass);
                body1.linearVelocity.z -= (impulse.z * body1.inverseMass);

                Fix64 num0, num1, num2;
                num0 = relativePos1.y * impulse.z - relativePos1.z * impulse.y;
                num1 = relativePos1.z * impulse.x - relativePos1.x * impulse.z;
                num2 = relativePos1.x * impulse.y - relativePos1.y * impulse.x;

                Fix64 num3 =
                    (((num0 * body1.invInertiaWorld.m00) +
                    (num1 * body1.invInertiaWorld.m10)) +
                    (num2 * body1.invInertiaWorld.m20));
                Fix64 num4 =
                    (((num0 * body1.invInertiaWorld.m01) +
                    (num1 * body1.invInertiaWorld.m11)) +
                    (num2 * body1.invInertiaWorld.m21));
                Fix64 num5 =
                    (((num0 * body1.invInertiaWorld.m02) +
                    (num1 * body1.invInertiaWorld.m12)) +
                    (num2 * body1.invInertiaWorld.m22));

                body1.angularVelocity.x -= num3;
                body1.angularVelocity.y -= num4;
                body1.angularVelocity.z -= num5;
            }

            if (!treatBody2AsStatic)
            {

                body2.linearVelocity.x += (impulse.x * body2.inverseMass);
                body2.linearVelocity.y += (impulse.y * body2.inverseMass);
                body2.linearVelocity.z += (impulse.z * body2.inverseMass);

                Fix64 num0, num1, num2;
                num0 = relativePos2.y * impulse.z - relativePos2.z * impulse.y;
                num1 = relativePos2.z * impulse.x - relativePos2.x * impulse.z;
                num2 = relativePos2.x * impulse.y - relativePos2.y * impulse.x;

                Fix64 num3 =
                    (((num0 * body2.invInertiaWorld.m00) +
                    (num1 * body2.invInertiaWorld.m10)) +
                    (num2 * body2.invInertiaWorld.m20));
                Fix64 num4 =
                    (((num0 * body2.invInertiaWorld.m01) +
                    (num1 * body2.invInertiaWorld.m11)) +
                    (num2 * body2.invInertiaWorld.m21));
                Fix64 num5 =
                    (((num0 * body2.invInertiaWorld.m02) +
                    (num1 * body2.invInertiaWorld.m12)) +
                    (num2 * body2.invInertiaWorld.m22));

                body2.angularVelocity.x += num3;
                body2.angularVelocity.y += num4;
                body2.angularVelocity.z += num5;
            }


            #endregion
        }

        public void ApplyImpulse(FVector3 impulse)
        {
            #region INLINE - HighFrequency
            //JVector temp;

            if (!treatBody1AsStatic)
            {
                body1.linearVelocity.x -= (impulse.x * body1.inverseMass);
                body1.linearVelocity.y -= (impulse.y * body1.inverseMass);
                body1.linearVelocity.z -= (impulse.z * body1.inverseMass);

                Fix64 num0, num1, num2;
                num0 = relativePos1.y * impulse.z - relativePos1.z * impulse.y;
                num1 = relativePos1.z * impulse.x - relativePos1.x * impulse.z;
                num2 = relativePos1.x * impulse.y - relativePos1.y * impulse.x;

                Fix64 num3 =
                    (((num0 * body1.invInertiaWorld.m00) +
                    (num1 * body1.invInertiaWorld.m10)) +
                    (num2 * body1.invInertiaWorld.m20));
                Fix64 num4 =
                    (((num0 * body1.invInertiaWorld.m01) +
                    (num1 * body1.invInertiaWorld.m11)) +
                    (num2 * body1.invInertiaWorld.m21));
                Fix64 num5 =
                    (((num0 * body1.invInertiaWorld.m02) +
                    (num1 * body1.invInertiaWorld.m12)) +
                    (num2 * body1.invInertiaWorld.m22));

                body1.angularVelocity.x -= num3;
                body1.angularVelocity.y -= num4;
                body1.angularVelocity.z -= num5;
            }

            if (!treatBody2AsStatic)
            {

                body2.linearVelocity.x += (impulse.x * body2.inverseMass);
                body2.linearVelocity.y += (impulse.y * body2.inverseMass);
                body2.linearVelocity.z += (impulse.z * body2.inverseMass);

                Fix64 num0, num1, num2;
                num0 = relativePos2.y * impulse.z - relativePos2.z * impulse.y;
                num1 = relativePos2.z * impulse.x - relativePos2.x * impulse.z;
                num2 = relativePos2.x * impulse.y - relativePos2.y * impulse.x;

                Fix64 num3 =
                    (((num0 * body2.invInertiaWorld.m00) +
                    (num1 * body2.invInertiaWorld.m10)) +
                    (num2 * body2.invInertiaWorld.m20));
                Fix64 num4 =
                    (((num0 * body2.invInertiaWorld.m01) +
                    (num1 * body2.invInertiaWorld.m11)) +
                    (num2 * body2.invInertiaWorld.m21));
                Fix64 num5 =
                    (((num0 * body2.invInertiaWorld.m02) +
                    (num1 * body2.invInertiaWorld.m12)) +
                    (num2 * body2.invInertiaWorld.m22));

                body2.angularVelocity.x += num3;
                body2.angularVelocity.y += num4;
                body2.angularVelocity.z += num5;
            }


            #endregion
        }

        /// <summary>
        /// PrepareForIteration has to be called before <see cref="Iterate"/>.
        /// </summary>
        /// <param name="timestep">The timestep of the simulation.</param>
        public void PrepareForIteration(Fix64 timestep)
        {
            FVector3 dv = CalculateRelativeVelocity();
            Fix64 kNormal = Fix64.zero;

            FVector3 rantra = FVector3.zero;
            if (!treatBody1AsStatic)
            {
                kNormal += body1.inverseMass;

                if (!body1IsMassPoint)
                {
                    FVector3.Cross(ref relativePos1, ref normal, out rantra);
                    FVector3.Transform(ref rantra, ref body1.invInertiaWorld, out rantra);
                    FVector3.Cross(ref rantra, ref relativePos1, out rantra);
                }
            }

            FVector3 rbntrb = FVector3.zero;
            if (!treatBody2AsStatic)
            {
                kNormal += body2.inverseMass;

                if (!body2IsMassPoint)
                {
                    FVector3.Cross(ref relativePos2, ref normal, out rbntrb);
                    FVector3.Transform(ref rbntrb, ref body2.invInertiaWorld, out rbntrb);
                    FVector3.Cross(ref rbntrb, ref relativePos2, out rbntrb);
                }
            }

            if (!treatBody1AsStatic)
                kNormal += FVector3.Dot(ref rantra, ref normal);

            if (!treatBody2AsStatic)
                kNormal += FVector3.Dot(ref rbntrb, ref normal);

            massNormal = Fix64.one / kNormal;

            tangent = dv - FVector3.Dot(dv, normal) * normal;
            tangent.Normalize();

            Fix64 kTangent = Fix64.zero;

            if (treatBody1AsStatic) rantra.MakeZero();
            else
            {
                kTangent += body1.inverseMass;
  
                if (!body1IsMassPoint)
                {
                    FVector3.Cross(ref relativePos1, ref normal, out rantra);
                    FVector3.Transform(ref rantra, ref body1.invInertiaWorld, out rantra);
                    FVector3.Cross(ref rantra, ref relativePos1, out rantra);
                }

            }

            if (treatBody2AsStatic)
                rbntrb.MakeZero();
            else
            {
                kTangent += body2.inverseMass;

                if (!body2IsMassPoint)
                {
                    FVector3.Cross(ref relativePos2, ref tangent, out rbntrb);
                    FVector3.Transform(ref rbntrb, ref body2.invInertiaWorld, out rbntrb);
                    FVector3.Cross(ref rbntrb, ref relativePos2, out rbntrb);
                }
            }

            if (!treatBody1AsStatic)
                kTangent += FVector3.Dot(ref rantra, ref tangent);
            if (!treatBody2AsStatic)
                kTangent += FVector3.Dot(ref rbntrb, ref tangent);

            massTangent = Fix64.one / kTangent;

            restitutionBias = lostSpeculativeBounce;

            speculativeVelocity = Fix64.zero;

            Fix64 relNormalVel = FVector3.Dot(ref normal, ref dv);

            if (Penetration > settings.allowedPenetration)
            {
                restitutionBias = settings.bias * (Fix64.one / timestep) * Fix64.Max(Fix64.zero, Penetration - settings.allowedPenetration);
                restitutionBias = DMath.Clamp(restitutionBias, Fix64.zero, settings.maximumBias);
              //  body1IsMassPoint = body2IsMassPoint = false;
            }
      

            Fix64 timeStepRatio = timestep / lastTimeStep;
            accumulatedNormalImpulse *= timeStepRatio;
            accumulatedTangentImpulse *= timeStepRatio;

            {
                // Static/Dynamic friction
                Fix64 relTangentVel = -FVector3.Dot(ref tangent, ref dv);
                Fix64 tangentImpulse = massTangent * relTangentVel;
                Fix64 maxTangentImpulse = -staticFriction * accumulatedNormalImpulse;

                if (tangentImpulse < maxTangentImpulse)
                    friction = dynamicFriction;
                else
                    friction = staticFriction;
            }

            FVector3 impulse;

            // Simultaneos solving and restitution is simply not possible
            // so fake it a bit by just applying restitution impulse when there
            // is a new contact.
            if (relNormalVel < -Fix64.one && newContact)
            {
                restitutionBias = Fix64.Max(-restitution * relNormalVel, restitutionBias);
            }

            // Speculative Contacts!
            // if the penetration is negative (which means the bodies are not already in contact, but they will
            // be in the future) we store the current bounce bias in the variable 'lostSpeculativeBounce'
            // and apply it the next frame, when the speculative contact was already solved.
            if (penetration < -settings.allowedPenetration)
            {
                speculativeVelocity = penetration / timestep;

                lostSpeculativeBounce = restitutionBias;
                restitutionBias = Fix64.zero;
            }
            else
            {
                lostSpeculativeBounce = Fix64.zero;
            }

            impulse = normal * accumulatedNormalImpulse + tangent * accumulatedTangentImpulse;
            ApplyImpulse(ref impulse);

            lastTimeStep = timestep;

            newContact = false;
        }

        public void TreatBodyAsStatic(RigidBodyIndex index)
        {
            if (index == RigidBodyIndex.RigidBody1) treatBody1AsStatic = true;
            else treatBody2AsStatic = true;
        }


        /// <summary>
        /// Initializes a contact.
        /// </summary>
        /// <param name="body1">The first body.</param>
        /// <param name="body2">The second body.</param>
        /// <param name="point1">The collision point in worldspace</param>
        /// <param name="point2">The collision point in worldspace</param>
        /// <param name="n">The normal pointing to body2.</param>
        /// <param name="penetration">The estimated penetration depth.</param>
        public void Initialize(RigidBody body1, RigidBody body2, ref FVector3 point1, ref FVector3 point2, ref FVector3 n,
            Fix64 penetration, bool newContact, ContactSettings settings)
        {
            this.body1 = body1;  this.body2 = body2;
            this.normal = n; normal.Normalize();
            this.p1 = point1; this.p2 = point2;

            this.newContact = newContact;

            FVector3.Subtract(ref p1, ref body1.position, out relativePos1);
            FVector3.Subtract(ref p2, ref body2.position, out relativePos2);
            FVector3.Transform(ref relativePos1, ref body1.invOrientation, out realRelPos1);
            FVector3.Transform(ref relativePos2, ref body2.invOrientation, out realRelPos2);

            this.initialPen = penetration;
            this.penetration = penetration;

            body1IsMassPoint = body1.isParticle;
            body2IsMassPoint = body2.isParticle;

            // Material Properties
            if (newContact)
            {
                treatBody1AsStatic = body1.isStatic;
                treatBody2AsStatic = body2.isStatic;

                accumulatedNormalImpulse = Fix64.zero;
                accumulatedTangentImpulse = Fix64.zero;

                lostSpeculativeBounce = Fix64.zero;

                switch (settings.MaterialCoefficientMixing)
                {
                    case ContactSettings.MaterialCoefficientMixingType.TakeMaximum:
                        staticFriction = Fix64.Max(body1.staticFriction, body2.staticFriction);
                        dynamicFriction = Fix64.Max(body1.staticFriction, body2.staticFriction);
                        restitution = Fix64.Max(body1.restitution, body2.restitution);
                        break;
                    case ContactSettings.MaterialCoefficientMixingType.TakeMinimum:
                        staticFriction = Fix64.Min(body1.staticFriction, body2.staticFriction);
                        dynamicFriction = Fix64.Min(body1.staticFriction, body2.staticFriction);
                        restitution = Fix64.Min(body1.restitution, body2.restitution);
                        break;
                    case ContactSettings.MaterialCoefficientMixingType.UseAverage:
                        staticFriction = (body1.staticFriction + body2.staticFriction) * Fix64.half;
                        dynamicFriction = (body1.staticFriction + body2.staticFriction) * Fix64.half;
                        restitution = (body1.restitution + body2.restitution) * Fix64.half;
                        break;
                }

            }

            this.settings = settings;
        }
    }
}