﻿
namespace Common.FrameDriving
{
    /// <summary>
    /// 为了避免生成帧需要频繁分配，内部使用的frame由IFramePool对象分配和回收。
    /// </summary>
    public interface IFramePool
    {
        /// <summary>
        /// 分配frame对象。
        /// </summary>
        /// <returns></returns>
        Frame Alloc();

        /// <summary>
        /// 回收一个frame对象。
        /// </summary>
        /// <param name="frame"></param>
        void Free(Frame frame);
    }
}
