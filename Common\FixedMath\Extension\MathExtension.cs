
using FixedMath;
using System.Runtime.Remoting.Messaging;
using UnityEngine;

public static partial class MathExtension
{

    #region  FVector Extension
    public static FVector2 ToFVector2(this Vector2 vec)
    {
        return new FVector2(
            Fix64.From<PERSON><PERSON><PERSON>(vec.x),
            Fix64.FromSingle(vec.y));
    }

    public static FVector3 ToFVector3(this Vector3 vec)
    {
        return new FVector3(
            Fix64.From<PERSON><PERSON><PERSON>(vec.x),
            Fix64.From<PERSON><PERSON><PERSON>(vec.y),
            Fix64.FromSingle(vec.z));
    }

    public static FVector4 ToLVector4(this Vector4 vec)
    {
        return new FVector4(
            Fix64.From<PERSON><PERSON><PERSON>(vec.x),
            Fix64.From<PERSON><PERSON><PERSON>(vec.y),
            Fix64.From<PERSON><PERSON><PERSON>(vec.z),
            Fix64.FromSingle(vec.w)
            );
    }

    public static Vector2 ToVector2(this FVector2 vec)
    {
        return new Vector2(vec.x.<PERSON>(), vec.y.<PERSON>());
    }
    public static Vector3 ToVector3(this FVector2 vec)
    {
        return new Vector3(vec.x.<PERSON>(), vec.y.ToSingle(), 0);
    }

    public static Vector3 ToVector3(this FVector3 vec)
    {
        return new Vector3(vec.x.ToSingle(), vec.y.ToSingle(), vec.z.ToSingle());
    }

    public static Vector4 ToVector4(this FVector4 vec)
    {
        return new Vector4(vec.x.ToSingle(), vec.y.ToSingle(), vec.z.ToSingle(), vec.w.ToSingle());
    }

    public static Vector3[] ToVector3Arrary(this FVector3[] arr)
    {
        int length = arr.Length;
        Vector3[] ret = new Vector3[length];
        for (int i = 0; i < arr.Length; i++)
        {
            ret[i] = arr[i].ToVector3();
        }
        return ret;
    }

    #endregion  FVector Extension 


    #region  FQuaternion Extension
    public static Quaternion ToQuaternion(this FQuaternion quat)
    {
        return new Quaternion(quat.x.ToSingle(), quat.y.ToSingle(), quat.z.ToSingle(), quat.w.ToSingle());
    }

    public static FQuaternion ToFQuaternion(this Quaternion quat)
    {
        return new FQuaternion(quat.x, quat.y, quat.z, quat.w);
    }

    #endregion FQuaternion Extension


    #region FMatrix3x3 Extension
    public static Matrix4x4 ToMatrix4x4(this FMatrix3x3 fmat3)
    {
        Matrix4x4 mat4 = new Matrix4x4();
        mat4.m33 = 1.0f;

        mat4.m00 = fmat3.m00;
        mat4.m01 = fmat3.m01;
        mat4.m02 = fmat3.m02;

        mat4.m10 = fmat3.m10;
        mat4.m11 = fmat3.m11;
        mat4.m12 = fmat3.m12;

        mat4.m20 = fmat3.m20;
        mat4.m21 = fmat3.m21;
        mat4.m22 = fmat3.m22;

        return mat4;
    }

    #endregion FMatrix3x3 Extension
}
